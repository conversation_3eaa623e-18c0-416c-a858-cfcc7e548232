{"$schema": "../node_modules/@tauri-apps/cli/config.schema.json", "productName": "Mind Elixir", "version": "1.0.2", "identifier": "com.timerecord.mindelixir", "build": {"beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "devUrl": "http://localhost:5174", "frontendDist": "../dist"}, "app": {"windows": [{"title": "Mind Elixir Desktop", "width": 1024, "height": 768, "resizable": true, "fullscreen": false, "dragDropEnabled": false}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}, "plugins": {"deep-link": {"mobile": [{"host": "your.website.com", "pathPrefix": ["/open"]}, {"host": "another.site.br"}], "desktop": {"schemes": ["mind-elixir"]}}}}