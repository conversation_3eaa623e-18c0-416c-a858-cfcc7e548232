{"rustc": 10895048813736897673, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[348261826933615366, "build_script_build", false, 10030132279723721789], [14039947826026167952, "build_script_build", false, 18030809649626633329], [5719423723759041893, "build_script_build", false, 9087906632100217485], [5157003953992891593, "build_script_build", false, 4846495474676679190], [13592916204794590741, "build_script_build", false, 8660247667140577421], [13785345585651898384, "build_script_build", false, 14044128139468826668], [7236291379133587555, "build_script_build", false, 1619313471717329285], [13087686409549645113, "build_script_build", false, 12629036546652593457], [12676100885892732016, "build_script_build", false, 6857633176627967104]], "local": [{"RerunIfChanged": {"output": "debug\\build\\MindElixirDesktop-a3ec402e8d53d893\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}