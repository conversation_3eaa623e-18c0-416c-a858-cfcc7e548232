{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[]", "target": 12764559200477346517, "profile": 8731458305071235362, "path": 4942398508502643691, "deps": [[348261826933615366, "app_lib", false, 12522052124950563002], [348261826933615366, "build_script_build", false, 11434041347260819693], [1288403060204016458, "tokio_util", false, 4074365969030864079], [3342462180580786304, "rmcp", false, 14744405186264718514], [3611029251930514425, "aes_gcm", false, 7791924392589732927], [5138218615291878843, "tokio", false, 8644082823235451780], [5157003953992891593, "tauri_plugin_dialog", false, 8292077993123775108], [5719423723759041893, "tauri_plugin_deep_link", false, 939922427124202513], [5986029879202738730, "log", false, 7455967408182446793], [7236291379133587555, "tauri_plugin_log", false, 14302564331830889285], [8606274917505247608, "tracing", false, 2916021035933069534], [9689903380558560274, "serde", false, 12490469357623971826], [9897246384292347999, "chrono", false, 6194448287398490250], [10806645703491011684, "thiserror", false, 9225620517626711244], [12676100885892732016, "tauri_plugin_os", false, 4972476883743020675], [13077212702700853852, "base64", false, 17291772695025635561], [13087686409549645113, "tauri_plugin_opener", false, 2102833348494840495], [13592916204794590741, "tauri_plugin_fs", false, 3106519017641072217], [13625485746686963219, "anyhow", false, 16464987559711244302], [13785345585651898384, "tauri_plugin_http", false, 7022071493261989464], [14039947826026167952, "tauri", false, 7100835771758387231], [14177399613791699417, "tauri_plugin_single_instance", false, 1661837986659827220], [15367738274754116744, "serde_json", false, 7866146061642808786], [16230660778393187092, "tracing_subscriber", false, 6893345429482413777], [17809372758784730012, "axum", false, 2441090115036911642]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\MindElixirDesktop-fb15dea9a13a97c6\\dep-bin-MindElixirDesktop", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}