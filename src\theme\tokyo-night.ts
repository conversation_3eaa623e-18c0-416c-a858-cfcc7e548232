import { Theme } from 'mind-elixir'

// https://github.com/tokyo-night/tokyo-night-vscode-theme
const tokyoNight: Theme = {
  name: 'Tokyo Night',
  type: 'dark',
  palette: ['#f7768e', '#ff9e64', '#e0af68', '#cfc9c2', '#9ece6a', '#73daca', '#b4f9f8', '#2ac3de', '#7dcfff', '#7aa2f7', '#bb9af7', '#c0caf5'],
  cssVar: {
    '--root-color': '#9aa5ce',
    '--root-bgcolor': '#24283b',
    '--root-radius': '15px',
    '--main-color': '#9aa5ce',
    '--main-bgcolor': '#24283b',
    '--color': '#9aa5ce',
    '--bgcolor': '#1a1b26',
  },
}

export default tokyoNight
