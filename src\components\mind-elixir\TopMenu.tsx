import { MindElixirInstance, NodeObj } from 'mind-elixir'
import {
  Menubar,
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarSeparator,
  MenubarSub,
  MenubarSubContent,
  MenubarSubTrigger,
  MenubarTrigger,
} from '../ui/menubar'
import { useEffect, useState, useCallback } from 'react'
import { useTheme } from '../theme-provider'
import { useTranslation } from 'react-i18next'
import ShortcutsDialog from './ShortcutsDialog'
import { confirm } from '@tauri-apps/plugin-dialog'
import { useLocation } from 'wouter'
import { downloadMethodList } from '@/utils/download'
import { Button } from '../ui/button'
import { LucideChevronLeft, BrainCircuit, TableOfContents, Save } from 'lucide-react'
import { useMindElixirStore } from '@/store'
import { MindElixirLocal, saveMap } from '@/api/map'
import { toast } from 'sonner'
import { getCurrentWindow } from '@tauri-apps/api/window'
import { themeMap } from '@/theme'

const setMindMapTheme = (mei: MindElixirInstance, theme: string) => {
  mei.changeTheme(themeMap[theme])
}

type Props = {
  mei: MindElixirInstance
  file?: MindElixirLocal
  mode: 'map' | 'outline'
  setMode: (mode: 'map' | 'outline') => void
  outlinerData: NodeObj[]
  setOutlinerData: (data: NodeObj[]) => void
  isUnsaved: boolean
  setIsUnsaved: (isUnsaved: boolean) => void
}
const TopMenu = ({ mei, file, mode, setMode, outlinerData, setOutlinerData, isUnsaved, setIsUnsaved }: Props) => {
  const [, navigate] = useLocation()
  const { setTheme } = useTheme()
  const { t } = useTranslation()
  const [shortcutsOpen, setShortcutsOpen] = useState(false)
  const params = new URLSearchParams(window.location.search)
  const id = params.get('id')!
  const saveFile = useCallback(async () => {
    const data = mei.getData()
    if (!data.nodeData) throw new Error('nodeData is undefined')
    const update: Partial<MindElixirLocal> = file || {
      id,
      createdAt: +Date.now(),
    }
    update.updatedAt = +Date.now()
    update.content = data
    await saveMap(id!, update as MindElixirLocal)
    toast.success(t('mindmap.saved'))
    setIsUnsaved(false)
  }, [mei, file, id, t, setIsUnsaved])
  const handleModeSwitch = useCallback(() => {
    if (mode === 'map') {
      setMode('outline')
      const data = mei.getData()
      setOutlinerData([data.nodeData])
    } else {
      setMode('map')
      mei.nodeData = outlinerData[0]
      mei.refresh()
    }
  }, [mode, setMode, mei, setOutlinerData, outlinerData])
  const handleKeydown = useCallback(
    async (e: KeyboardEvent) => {
      if (e.key === 's' && (e.ctrlKey || e.metaKey)) {
        e.preventDefault()
        saveFile()
      } else if (e.key === 'o' && (e.ctrlKey || e.metaKey)) {
        e.preventDefault()
        handleModeSwitch()
      }
    },
    [saveFile, handleModeSwitch]
  )
  useEffect(() => {
    const handleOperation = () => {
      // debugger
      setIsUnsaved(true)
    }
    mei.bus.addListener('operation', handleOperation)
    mei.bus.addListener('expandNode', handleOperation)
    // mei.bus.addListener('linkDiv', handleOperation)
    mei.bus.addListener('updateArrowDelta', handleOperation)
    document.addEventListener('keydown', handleKeydown)
    return () => {
      // bus 不用管了，
      // 父组件会 destroy，
      // react 团队本身就没有解决清除顺序的问题 https://github.com/facebook/react/issues/16728
      // mei.bus.removeListener('operation', handleOperation)
      document.removeEventListener('keydown', handleKeydown)
    }
  }, [mei, file, setIsUnsaved, handleKeydown])

  useEffect(() => {
    const unlisten = getCurrentWindow().onCloseRequested(async e => {
      if (isUnsaved) {
        const confirmation = await confirm(t('dialog.unsavedChangesAlert'), { title: 'Mind Elixir Desktop', kind: 'warning' })
        if (!confirmation) e.preventDefault()
      }
    })

    return () => {
      unlisten.then(unlisten => {
        unlisten()
      })
    }
  }, [isUnsaved, t])

  return (
    <div className="fixed flex gap-2 top-3 left-3 z-50">
      <Button
        variant="outline"
        onClick={async () => {
          if (isUnsaved) {
            const confirmation = await confirm(t('dialog.unsavedChangesAlert'), { title: 'Mind Elixir Desktop', kind: 'warning' })
            if (!confirmation) return
          }
          window.history.back()
        }}
      >
        <LucideChevronLeft />
      </Button>
      <Menubar>
        <MenubarMenu>
          <MenubarTrigger>{t('menu.mindElixir')}</MenubarTrigger>
          <MenubarContent>
            <MenubarSub>
              <MenubarSubTrigger>{t('menu.export')}</MenubarSubTrigger>
              <MenubarSubContent>
                {downloadMethodList.map(item => {
                  return <MenubarItem onClick={() => item.download(mei)}>{item.type}</MenubarItem>
                })}
              </MenubarSubContent>
            </MenubarSub>
            <MenubarSeparator />
            <MenubarSub>
              <MenubarSubTrigger>{t('menu.theme')}</MenubarSubTrigger>
              <MenubarSubContent>
                {Object.keys(themeMap).map(key => {
                  return (
                    <MenubarItem key={key} onClick={() => setMindMapTheme(mei, key)}>
                      {key.toUpperCase()}
                    </MenubarItem>
                  )
                })}
              </MenubarSubContent>
            </MenubarSub>
            <MenubarSub>
              <MenubarSubTrigger>{t('menu.appearance')}</MenubarSubTrigger>
              <MenubarSubContent>
                <MenubarItem onClick={() => setTheme('system')}>{t('menu.system')}</MenubarItem>
                <MenubarItem onClick={() => setTheme('light')}>{t('menu.light')}</MenubarItem>
                <MenubarItem onClick={() => setTheme('dark')}>{t('menu.dark')}</MenubarItem>
              </MenubarSubContent>
            </MenubarSub>
            <MenubarSeparator />
            <MenubarItem onClick={() => setShortcutsOpen(true)}>{t('menu.shortcuts')}</MenubarItem>
            <MenubarSeparator />
            <MenubarItem onClick={() => navigate('/')}>{t('menu.exit')}</MenubarItem>
          </MenubarContent>
        </MenubarMenu>
      </Menubar>
      <Button variant="outline" onClick={handleModeSwitch}>
        {mode === 'map' ? <BrainCircuit /> : <TableOfContents />}
      </Button>
      <Button variant="outline" onClick={saveFile}>
        <Save color={isUnsaved ? 'red' : 'currentColor'} />
      </Button>
      <ShortcutsDialog open={shortcutsOpen} setOpen={setShortcutsOpen} />
    </div>
  )
}

const WrappedTopMenu = (props: Omit<Props, 'mei'>) => {
  const mei = useMindElixirStore(state => state.mei)
  const file = useMindElixirStore(state => state.file)
  return mei ? <TopMenu mei={mei} file={file} {...props} /> : null
}

export default WrappedTopMenu
