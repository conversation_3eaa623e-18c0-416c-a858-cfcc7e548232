import { MindElixirLocal } from '@/api/map'

export const templates: MindElixirLocal[] = [
  {
    id: 'template-1',
    content: {
      direction: 2,
      nodeData: {
        id: '1',
        topic: '项目计划',
        children: [
          {
            id: '2',
            topic: '目标',
            children: [
              { id: '16', topic: '项目愿景' },
              { id: '17', topic: '核心目标' },
              { id: '18', topic: '成功标准' },
              { id: '19', topic: '可交付成果' },
            ],
          },
          {
            id: '3',
            topic: '时间线',
            children: [
              { id: '20', topic: '里程碑' },
              { id: '21', topic: '关键节点' },
              { id: '22', topic: '阶段划分' },
              { id: '23', topic: '交付日期' },
            ],
          },
          {
            id: '4',
            topic: '资源',
            children: [
              { id: '24', topic: '团队成员' },
              { id: '25', topic: '预算' },
              { id: '26', topic: '工具/设备' },
              { id: '27', topic: '外部支持' },
            ],
          },
          {
            id: '5',
            topic: '风险',
            children: [
              { id: '28', topic: '潜在问题' },
              { id: '29', topic: '应对策略' },
              { id: '30', topic: '预警指标' },
              { id: '31', topic: '备选方案' },
            ],
          },
          {
            id: '32',
            topic: '沟通计划',
            children: [
              { id: '33', topic: '会议安排' },
              { id: '34', topic: '汇报机制' },
              { id: '35', topic: '协作工具' },
              { id: '36', topic: '文档管理' },
            ],
          },
          {
            id: '37',
            topic: '质量控制',
            children: [
              { id: '38', topic: '质量标准' },
              { id: '39', topic: '审核流程' },
              { id: '40', topic: '测试计划' },
              { id: '41', topic: '验收标准' },
            ],
          },
        ],
      },
    },
    createdAt: Date.now(),
    updatedAt: Date.now(),
  },
  {
    id: 'template-2',
    content: {
      direction: 2,
      nodeData: {
        id: '6',
        topic: '头脑风暴',
        children: [
          {
            id: '7',
            topic: '主要想法',
            children: [
              { id: '42', topic: '核心概念' },
              { id: '43', topic: '创新点' },
              { id: '44', topic: '关键特性' },
              { id: '45', topic: '独特优势' },
            ],
          },
          {
            id: '8',
            topic: '相关概念',
            children: [
              { id: '46', topic: '市场趋势' },
              { id: '47', topic: '竞品分析' },
              { id: '48', topic: '用户需求' },
              { id: '49', topic: '技术可行性' },
            ],
          },
          {
            id: '9',
            topic: '可能性探索',
            children: [
              { id: '50', topic: '商业模式' },
              { id: '51', topic: '发展方向' },
              { id: '52', topic: '潜在机会' },
              { id: '53', topic: '创新空间' },
            ],
          },
          {
            id: '10',
            topic: '行动计划',
            children: [
              { id: '54', topic: '近期目标' },
              { id: '55', topic: '资源需求' },
              { id: '56', topic: '执行步骤' },
              { id: '57', topic: '时间节点' },
            ],
          },
          {
            id: '58',
            topic: '问题分析',
            children: [
              { id: '59', topic: '现有痛点' },
              { id: '60', topic: '潜在风险' },
              { id: '61', topic: '限制因素' },
              { id: '62', topic: '解决方案' },
            ],
          },
          {
            id: '63',
            topic: '反馈收集',
            children: [
              { id: '64', topic: '用户反馈' },
              { id: '65', topic: '团队建议' },
              { id: '66', topic: '专家意见' },
              { id: '67', topic: '市场响应' },
            ],
          }
        ],
      },
    },
    createdAt: Date.now(),
    updatedAt: Date.now(),
  },
  {
    id: 'template-3',
    content: {
      direction: 2,
      nodeData: {
        id: '11',
        topic: '学习笔记',
        children: [
          {
            id: '12',
            topic: '关键概念',
            children: [
              { id: '68', topic: '基本定义' },
              { id: '69', topic: '核心原理' },
              { id: '70', topic: '重要公式' },
              { id: '71', topic: '专业术语' },
            ],
          },
          {
            id: '13',
            topic: '重要细节',
            children: [
              { id: '72', topic: '注意事项' },
              { id: '73', topic: '常见误区' },
              { id: '74', topic: '特殊情况' },
              { id: '75', topic: '补充说明' },
            ],
          },
          {
            id: '14',
            topic: '例子',
            children: [
              { id: '76', topic: '典型案例' },
              { id: '77', topic: '实际应用' },
              { id: '78', topic: '练习题' },
              { id: '79', topic: '解题思路' },
            ],
          },
          {
            id: '15',
            topic: '复习点',
            children: [
              { id: '80', topic: '重点内容' },
              { id: '81', topic: '难点解析' },
              { id: '82', topic: '易错点' },
              { id: '83', topic: '考试重点' },
            ],
          },
          {
            id: '84',
            topic: '学习资源',
            children: [
              { id: '85', topic: '参考书籍' },
              { id: '86', topic: '在线课程' },
              { id: '87', topic: '相关文章' },
              { id: '88', topic: '学习工具' },
            ],
          },
          {
            id: '89',
            topic: '学习计划',
            children: [
              { id: '90', topic: '学习目标' },
              { id: '91', topic: '时间安排' },
              { id: '92', topic: '进度跟踪' },
              { id: '93', topic: '复习计划' },
            ],
          }
        ],
      },
    },
    createdAt: Date.now(),
    updatedAt: Date.now(),
  },
  {
    id: 'template-4',
    content: {
      direction: 2,
      nodeData: {
        id: '94',
        topic: '问题分析',
        children: [
          {
            id: '95',
            topic: '问题描述',
            children: [
              { id: '96', topic: '现象表现' },
              { id: '97', topic: '影响范围' },
              { id: '98', topic: '紧急程度' },
              { id: '99', topic: '相关方' },
            ],
          },
          {
            id: '100',
            topic: '原因分析',
            children: [
              { id: '101', topic: '直接原因' },
              { id: '102', topic: '根本原因' },
              { id: '103', topic: '诱因' },
              { id: '104', topic: '关联因素' },
            ],
          },
          {
            id: '105',
            topic: '解决方案',
            children: [
              { id: '106', topic: '临时措施' },
              { id: '107', topic: '长期方案' },
              { id: '108', topic: '备选方案' },
              { id: '109', topic: '成本评估' },
            ],
          },
          {
            id: '110',
            topic: '执行计划',
            children: [
              { id: '111', topic: '任务分解' },
              { id: '112', topic: '责任分工' },
              { id: '113', topic: '时间节点' },
              { id: '114', topic: '所需资源' },
            ],
          },
          {
            id: '115',
            topic: '风险评估',
            children: [
              { id: '116', topic: '潜在风险' },
              { id: '117', topic: '预防措施' },
              { id: '118', topic: '应急预案' },
              { id: '119', topic: '风险等级' },
            ],
          },
          {
            id: '120',
            topic: '效果评估',
            children: [
              { id: '121', topic: '评估指标' },
              { id: '122', topic: '数据收集' },
              { id: '123', topic: '改进建议' },
              { id: '124', topic: '经验总结' },
            ],
          }
        ],
      },
    },
    createdAt: Date.now(),
    updatedAt: Date.now(),
  },
  {
    id: 'template-5',
    content: {
      direction: 2,
      nodeData: {
        id: '125',
        topic: '读书笔记',
        children: [
          {
            id: '126',
            topic: '基本信息',
            children: [
              { id: '127', topic: '书名/标题' },
              { id: '128', topic: '作者信息' },
              { id: '129', topic: '出版信息' },
              { id: '130', topic: '阅读日期' },
            ],
          },
          {
            id: '131',
            topic: '内容概要',
            children: [
              { id: '132', topic: '核心观点' },
              { id: '133', topic: '主要论据' },
              { id: '134', topic: '研究方法' },
              { id: '135', topic: '结论发现' },
            ],
          },
          {
            id: '136',
            topic: '章节笔记',
            children: [
              { id: '137', topic: '重要段落' },
              { id: '138', topic: '关键引用' },
              { id: '139', topic: '疑问标注' },
              { id: '140', topic: '页码索引' },
            ],
          },
          {
            id: '141',
            topic: '个人思考',
            children: [
              { id: '142', topic: '启发感悟' },
              { id: '143', topic: '质疑思考' },
              { id: '144', topic: '关联延伸' },
              { id: '145', topic: '实践应用' },
            ],
          },
          {
            id: '146',
            topic: '知识梳理',
            children: [
              { id: '147', topic: '概念框架' },
              { id: '148', topic: '知识图谱' },
              { id: '149', topic: '主题归类' },
              { id: '150', topic: '跨书联系' },
            ],
          },
          {
            id: '151',
            topic: '行动计划',
            children: [
              { id: '152', topic: '复习计划' },
              { id: '153', topic: '分享交流' },
              { id: '154', topic: '实践目标' },
              { id: '155', topic: '后续阅读' },
            ],
          }
        ],
      },
    },
    createdAt: Date.now(),
    updatedAt: Date.now(),
  },
  {
    id: 'template-6',
    content: {
      direction: 2,
      nodeData: {
        id: '156',
        topic: '产品设计',
        children: [
          {
            id: '157',
            topic: '用户研究',
            children: [
              { id: '158', topic: '目标用户' },
              { id: '159', topic: '用户痛点' },
              { id: '160', topic: '使用场景' },
              { id: '161', topic: '用户反馈' },
            ],
          },
          {
            id: '162',
            topic: '市场分析',
            children: [
              { id: '163', topic: '竞品分析' },
              { id: '164', topic: '市场规模' },
              { id: '165', topic: '发展趋势' },
              { id: '166', topic: '商业模式' },
            ],
          },
          {
            id: '167',
            topic: '功能规划',
            children: [
              { id: '168', topic: '核心功能' },
              { id: '169', topic: '创新特性' },
              { id: '170', topic: '技术可行性' },
              { id: '171', topic: '迭代计划' },
            ],
          },
          {
            id: '172',
            topic: '交互设计',
            children: [
              { id: '173', topic: '信息架构' },
              { id: '174', topic: '交互流程' },
              { id: '175', topic: '界面布局' },
              { id: '176', topic: '操作逻辑' },
            ],
          },
          {
            id: '177',
            topic: '体验优化',
            children: [
              { id: '178', topic: '性能指标' },
              { id: '179', topic: '易用性' },
              { id: '180', topic: '视觉风格' },
              { id: '181', topic: '反馈机制' },
            ],
          },
          {
            id: '182',
            topic: '发布策略',
            children: [
              { id: '183', topic: '测试方案' },
              { id: '184', topic: '运营计划' },
              { id: '185', topic: '推广渠道' },
              { id: '186', topic: '数据监控' },
            ],
          }
        ],
      },
    },
    createdAt: Date.now(),
    updatedAt: Date.now(),
  },
  {
    id: 'template-7',
    content: {
      direction: 2,
      nodeData: {
        id: '187',
        topic: '个人规划',
        children: [
          {
            id: '188',
            topic: '目标设定',
            children: [
              { id: '189', topic: '短期目标' },
              { id: '190', topic: '中期目标' },
              { id: '191', topic: '长期目标' },
              { id: '192', topic: '人生愿景' },
            ],
          },
          {
            id: '193',
            topic: '能力提升',
            children: [
              { id: '194', topic: '专业技能' },
              { id: '195', topic: '软实力' },
              { id: '196', topic: '知识储备' },
              { id: '197', topic: '学习计划' },
            ],
          },
          {
            id: '198',
            topic: '职业发展',
            children: [
              { id: '199', topic: '职业方向' },
              { id: '200', topic: '晋升路径' },
              { id: '201', topic: '行业积累' },
              { id: '202', topic: '人脉拓展' },
            ],
          },
          {
            id: '203',
            topic: '生活平衡',
            children: [
              { id: '204', topic: '健康管理' },
              { id: '205', topic: '时间规划' },
              { id: '206', topic: '兴趣爱好' },
              { id: '207', topic: '家庭关系' },
            ],
          },
          {
            id: '208',
            topic: '资源规划',
            children: [
              { id: '209', topic: '财务计划' },
              { id: '210', topic: '学习资源' },
              { id: '211', topic: '人际资源' },
              { id: '212', topic: '工具利用' },
            ],
          },
          {
            id: '213',
            topic: '行动管理',
            children: [
              { id: '214', topic: '任务分解' },
              { id: '215', topic: '时间节点' },
              { id: '216', topic: '执行跟踪' },
              { id: '217', topic: '效果评估' },
            ],
          }
        ],
      },
    },
    createdAt: Date.now(),
    updatedAt: Date.now(),
  },
  {
    id: 'template-8',
    content: {
      direction: 2,
      nodeData: {
        id: '218',
        topic: '会议记录',
        children: [
          {
            id: '219',
            topic: '会议信息',
            children: [
              { id: '220', topic: '会议主题' },
              { id: '221', topic: '时间地点' },
              { id: '222', topic: '参会人员' },
              { id: '223', topic: '会议类型' },
            ],
          },
          {
            id: '224',
            topic: '议程内容',
            children: [
              { id: '225', topic: '议题列表' },
              { id: '226', topic: '讨论要点' },
              { id: '227', topic: '决策事项' },
              { id: '228', topic: '遗留问题' },
            ],
          },
          {
            id: '229',
            topic: '工作分配',
            children: [
              { id: '230', topic: '任务清单' },
              { id: '231', topic: '负责人' },
              { id: '232', topic: '完成时间' },
              { id: '233', topic: '验收标准' },
            ],
          },
          {
            id: '234',
            topic: '资源需求',
            children: [
              { id: '235', topic: '人力资源' },
              { id: '236', topic: '设备物料' },
              { id: '237', topic: '预算支持' },
              { id: '238', topic: '其他支持' },
            ],
          },
          {
            id: '239',
            topic: '跟进计划',
            children: [
              { id: '240', topic: '检查点' },
              { id: '241', topic: '汇报机制' },
              { id: '242', topic: '协调事项' },
              { id: '243', topic: '风险预警' },
            ],
          },
          {
            id: '244',
            topic: '会议总结',
            children: [
              { id: '245', topic: '主要成果' },
              { id: '246', topic: '经验教训' },
              { id: '247', topic: '后续安排' },
              { id: '248', topic: '参考资料' },
            ],
          }
        ],
      },
    },
    createdAt: Date.now(),
    updatedAt: Date.now(),
  },
  {
    id: 'template-9',
    content: {
      direction: 2,
      nodeData: {
        id: '249',
        topic: '知识体系',
        children: [
          {
            id: '250',
            topic: '基础概念',
            children: [
              { id: '251', topic: '核心术语' },
              { id: '252', topic: '基本原理' },
              { id: '253', topic: '发展历史' },
              { id: '254', topic: '理论框架' },
            ],
          },
          {
            id: '255',
            topic: '技术要点',
            children: [
              { id: '256', topic: '关键技术' },
              { id: '257', topic: '实现方法' },
              { id: '258', topic: '最佳实践' },
              { id: '259', topic: '常见问题' },
            ],
          },
          {
            id: '260',
            topic: '应用场景',
            children: [
              { id: '261', topic: '典型案例' },
              { id: '262', topic: '实际应用' },
              { id: '263', topic: '使用限制' },
              { id: '264', topic: '发展趋势' },
            ],
          },
          {
            id: '265',
            topic: '工具资源',
            children: [
              { id: '266', topic: '开发工具' },
              { id: '267', topic: '学习资源' },
              { id: '268', topic: '社区支持' },
              { id: '269', topic: '相关标准' },
            ],
          },
          {
            id: '270',
            topic: '进阶主题',
            children: [
              { id: '271', topic: '高级特性' },
              { id: '272', topic: '优化技巧' },
              { id: '273', topic: '架构设计' },
              { id: '274', topic: '性能调优' },
            ],
          },
          {
            id: '275',
            topic: '实践路径',
            children: [
              { id: '276', topic: '入门指南' },
              { id: '277', topic: '进阶路线' },
              { id: '278', topic: '专家建议' },
              { id: '279', topic: '能力评估' },
            ],
          }
        ],
      },
    },
    createdAt: Date.now(),
    updatedAt: Date.now(),
  },
  {
    id: 'template-10',
    content: {
      direction: 2,
      nodeData: {
        id: '280',
        topic: '创业规划',
        children: [
          {
            id: '281',
            topic: '商业模式',
            children: [
              { id: '282', topic: '价值主张' },
              { id: '283', topic: '目标市场' },
              { id: '284', topic: '收入来源' },
              { id: '285', topic: '成本结构' },
            ],
          },
          {
            id: '286',
            topic: '市场分析',
            children: [
              { id: '287', topic: '市场规模' },
              { id: '288', topic: '竞争格局' },
              { id: '289', topic: '用户画像' },
              { id: '290', topic: '市场趋势' },
            ],
          },
          {
            id: '291',
            topic: '产品战略',
            children: [
              { id: '292', topic: '核心产品' },
              { id: '293', topic: '功能特性' },
              { id: '294', topic: '迭代计划' },
              { id: '295', topic: '技术路线' },
            ],
          },
          {
            id: '296',
            topic: '运营策略',
            children: [
              { id: '297', topic: '获客渠道' },
              { id: '298', topic: '用户运营' },
              { id: '299', topic: '品牌建设' },
              { id: '300', topic: '营销推广' },
            ],
          },
          {
            id: '301',
            topic: '团队建设',
            children: [
              { id: '302', topic: '组织架构' },
              { id: '303', topic: '人才招聘' },
              { id: '304', topic: '文化建设' },
              { id: '305', topic: '激励机制' },
            ],
          },
          {
            id: '306',
            topic: '财务规划',
            children: [
              { id: '307', topic: '融资计划' },
              { id: '308', topic: '现金流管理' },
              { id: '309', topic: '支出控制' },
              { id: '310', topic: '盈利预测' },
            ],
          }
        ],
      },
    },
    createdAt: Date.now(),
    updatedAt: Date.now(),
  },
]
