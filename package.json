{"name": "mind-elixir-desktop", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "strict-build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "tsc": "tsc -b"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@microsoft/fetch-event-source": "^2.0.1", "@mind-elixir/import-xmind": "1.0.2", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-aspect-ratio": "^1.1.4", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-context-menu": "^2.2.12", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-hover-card": "^1.1.11", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-menubar": "^1.1.12", "@radix-ui/react-navigation-menu": "^1.2.10", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toast": "^1.2.11", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-toggle-group": "^1.1.7", "@radix-ui/react-tooltip": "^1.2.4", "@ssshooter/modern-screenshot": "^4.5.0", "@tauri-apps/api": "^2.5.0", "@tauri-apps/plugin-deep-link": "~2", "@tauri-apps/plugin-dialog": "~2", "@tauri-apps/plugin-fs": "~2.2.1", "@tauri-apps/plugin-http": "~2.4.3", "@tauri-apps/plugin-opener": "^2.2.6", "@tauri-apps/plugin-os": "~2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "fast-xml-parser": "^5.2.5", "i18next": "^25.0.1", "input-otp": "^1.4.2", "jszip": "^3.10.1", "less": "^4.3.0", "lucide-react": "^0.503.0", "mind-elixir": "5.0.0-beta.46", "next-themes": "^0.4.6", "opfs-tools": "^0.7.2", "react": "^19.1.0", "react-colorful": "^5.6.1", "react-day-picker": "9.6.7", "react-dom": "^19.1.0", "react-hook-form": "^7.56.1", "react-i18next": "^15.5.1", "react-outliner-neo": "^0.2.0", "react-resizable-panels": "^2.1.8", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "wouter": "^3.7.0", "zod": "^3.24.3", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.25.1", "@tauri-apps/cli": "^2.5.0", "@types/node": "^22.14.1", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react-swc": "^3.9.0", "autoprefixer": "^10.4.21", "eslint": "^9.25.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.14", "typescript": "^5.8.3", "typescript-eslint": "^8.31.0", "vite": "^6.3.3"}}