import { aiReqStream, MindMapTransformStream } from '@/api/ai'
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuShortcut,
  ContextMenuSubContent,
  ContextMenuSubTrigger,
  ContextMenuTrigger,
} from '@/components/ui/context-menu'
import { operations } from '@/utils/operationMap'
import { ContextMenuSub } from '@radix-ui/react-context-menu'
import MindElixir, { MindElixirInstance, NodeObj } from 'mind-elixir'
import React from 'react'
import { Textarea } from '../ui/textarea'
import { toast } from 'sonner'
import { useTranslation } from 'react-i18next'
import { useMindElixirStore, useUserSettings } from '@/store'
import { convertToMd } from '@/utils/markdown'
import i18n from '@/i18n/config'
import { isMobile } from '@/utils/platform'

const aiChildren = async (node: NodeObj, mei: MindElixirInstance, prompt?: string) => {
  const loader = document.createElement('div')
  loader.className = 'loader'
  const el = MindElixir.E(node.id)
  el.appendChild(loader)

  const thinking = document.createElement('me-children')
  thinking.className = 'thinking'
  thinking.innerText = 'Thinking...'

  const setting = useUserSettings.getState()
  try {
    const context = setting.aiMode === 'context-aware' ? convertToMd(mei.getData().nodeData, node) : ''
    const stream = aiReqStream(node.topic, prompt, context)
    const transformer = new MindMapTransformStream()
    const reader = stream.pipeThrough(transformer).getReader()
    while (true) {
      const { done, value } = await reader.read()
      if (done) {
        break
      }
      if (value.type === 'thinkingStart') {
        node.note = node.note || ''
        if (node.children?.length) {
          el.parentElement.nextSibling.appendChild(thinking)
        } else {
          el.parentElement.parentElement.appendChild(thinking)
        }
        try {
          mei.linkDiv()
        } catch (e) {
          console.log(e)
        }
      } else if (value.type === 'thinkingEnd') {
        // node.note
        thinking.remove()
        mei.linkDiv()
      } else if (value.type === 'thinking') {
        node.note += value.payload.content
        thinking.innerText = node.note || ''
        thinking.scrollTop = 99999
      } else if (value.type === 'addChild') {
        mei.addChild(el, {
          topic: value.payload.topic || '',
          id: value.payload.id,
        })
      } else if (value.type === 'editTopic') {
        mei.setNodeTopic(MindElixir.E(value.payload.id), value.payload.topic || '')
      }
    }
    mei!.refresh()
  } catch (e: unknown) {
    console.trace(e)
    loader.remove()
    const err = JSON.stringify(e)
    toast.error(err + ' ' + i18n.t('settings.ai.checkSettings'))
  }
}

const MContextMenu = ({
  children,
}: {
  children: React.ReactNode
}) => {
  const mei = useMindElixirStore(state => state.mei)
  const node = useMindElixirStore(state => state.currentNode)
  const nodes = useMindElixirStore(state => state.currentNodes)
  const [prompt, setPrompt] = React.useState('')
  const { t } = useTranslation()

  // 在移动设备上禁用ContextMenu
  if (isMobile()) {
    return <div className="flex-grow w-96 context-menu-target">{children}</div>
  }

  // 4 种情况 root，多选，单选，没选
  return (
    <ContextMenu modal={false} onOpenChange={() => { }}>
      <ContextMenuTrigger className="flex-grow w-96 context-menu-target">{children}</ContextMenuTrigger>
      <ContextMenuContent className="w-64">
        {nodes.length === 1 && (
          <>
            <ContextMenuItem
              onClick={async () => {
                aiChildren(node!, mei!)
              }}
            >
              {t('contextMenu.ai')}
            </ContextMenuItem>
            <ContextMenuSub>
              <ContextMenuSubTrigger>{t('contextMenu.aiWithPrompt')}</ContextMenuSubTrigger>
              <ContextMenuSubContent className="w-48 flex flex-col gap-3">
                <Textarea
                  rows={5}
                  value={prompt}
                  onKeyDown={e => e.stopPropagation()}
                  onChange={e => {
                    setPrompt(e.target.value)
                  }}
                  placeholder={t('contextMenu.prompt')}
                />
                <ContextMenuItem
                  className="text-center"
                  onClick={() => {
                    aiChildren(node!, mei!, prompt)
                  }}
                >
                  {t('contextMenu.generate')}
                </ContextMenuItem>
              </ContextMenuSubContent>
            </ContextMenuSub>
          </>
        )}
        {operations
          .filter(item => {
            const isRoot = !mei?.currentNode?.nodeObj?.parent
            return !(isRoot && item.hideIfRoot)
          })
          .map(item => (
            <ContextMenuItem
              key={item.label}
              onClick={() => {
                setTimeout(() => {
                  // activeElement will changed after context menu closed
                  // so we have to delay the action
                  item.onClick(mei!)
                }, 200)
              }}
            >
              {t(item.label)}
              <ContextMenuShortcut>{item.shortcut}</ContextMenuShortcut>
            </ContextMenuItem>
          ))}
      </ContextMenuContent>
    </ContextMenu>
  )
}

export default MContextMenu
