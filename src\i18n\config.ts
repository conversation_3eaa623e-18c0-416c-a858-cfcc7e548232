import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import enTranslation from './locales/en.json'
import zhTranslation from './locales/zh.json'
import zhTWTranslation from './locales/zh-TW.json'
import jaTranslation from './locales/ja.json'
import koTranslation from './locales/ko.json'
import deTranslation from './locales/de.json'
import esTranslation from './locales/es.json'
import { useUserSettings } from '@/store'
import { locale } from '@tauri-apps/plugin-os'

// Get initial language from Zustand store
const getInitialLanguage = () => {
  return useUserSettings.getState().language
}

export const i18nInit = async () => {
  const sysLocale = await locale()
  const availableLanguages = ['en', 'zh', 'zh-TW', 'ja', 'ko', 'de', 'es']
  const systemLanguage = sysLocale && sysLocale.split('-')[0]
  const settingLanguage = getInitialLanguage()
  let lng = 'en'
  if (settingLanguage) {
    lng = settingLanguage
  } else if (systemLanguage) {
    if (availableLanguages.includes(systemLanguage)) {
      lng = systemLanguage
    }
  }
  console.log('System locale:', sysLocale)
  console.log('Setting language:', settingLanguage)
  console.log('Final language:', lng)
  if (!settingLanguage) {
    useUserSettings.setState({ language: lng })
  }

  i18n.use(initReactI18next).init({
    resources: {
      en: {
        translation: enTranslation,
      },
      zh: {
        translation: zhTranslation,
      },
      'zh-TW': {
        translation: zhTWTranslation,
      },
      ja: {
        translation: jaTranslation,
      },
      ko: {
        translation: koTranslation,
      },
      de: {
        translation: deTranslation,
      },
      es: {
        translation: esTranslation,
      },
    },
    lng,
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false,
    },
  })

  i18n.on('languageChanged', lng => (document.documentElement.lang = lng))
}

export default i18n
