# BaseListView 重构总结

## 概述
成功将 `MapList.tsx` 和 `TrashList.tsx` 的相似结构抽象为一个通用的 `BaseListView` 组件，避免了代码重复，提高了可维护性。

## 创建的文件
- `src/components/app/BaseListView.tsx` - 通用列表视图组件

## 修改的文件
- `src/components/app/MapList.tsx` - 重构为使用 BaseListView
- `src/components/app/TrashList.tsx` - 重构为使用 BaseListView

## BaseListView 特性

### 配置接口 (BaseListViewConfig)
```typescript
interface BaseListViewConfig {
  // API 配置
  listApi: (page: number, size: number) => Promise<{ total: number; list: (MindElixirLocal | MindElixirLoadError)[] }>
  searchDir: string
  cardType: 'map' | 'trash'
  
  // UI 配置
  actionButtons?: ReactNode
  dialogs?: ReactNode
  emptyStateContent?: ReactNode
  
  // 回调函数
  onListChange?: (total: number) => void
  
  // 高度配置
  mobileHeightOffset?: number
  desktopHeightOffset?: number
  mobileMinHeightOffset?: number
  desktopMinHeightOffset?: number
}
```

### 抽象的功能
1. **状态管理**: 分页、加载状态、移动端检测
2. **搜索功能**: 统一的搜索逻辑和UI
3. **响应式布局**: 移动端和桌面端适配
4. **卡片渲染**: 支持正常卡片和损坏卡片
5. **分页**: 统一的分页组件
6. **空状态**: 可配置的空状态显示

## MapList 配置
```typescript
const config: BaseListViewConfig = {
  listApi: listMaps,
  searchDir: 'maps',
  cardType: 'map',
  onListChange: (newTotal) => {
    setTotal(newTotal)
    invalidateTagCache()
  },
  actionButtons: (
    <div className="flex gap-2">
      <Button onClick={createMap}>新建</Button>
      <Button onClick={importMap}>导入</Button>
    </div>
  ),
  dialogs: (
    <>
      <PayDialog />
      <ImportDialog />
    </>
  ),
  emptyStateContent: (
    <div onClick={createMap}>
      <Plus />
      新建思维导图
    </div>
  )
}
```

## TrashList 配置
```typescript
const config: BaseListViewConfig = {
  listApi: listTrash,
  searchDir: 'trash',
  cardType: 'trash',
  actionButtons: (
    <Button onClick={() => setDialogOpen(true)}>
      清空回收站
    </Button>
  ),
  dialogs: (
    <DeleteAlertDialog />
  ),
  emptyStateContent: null // 使用默认空状态
}
```

## 优势
1. **代码复用**: 减少了约 80% 的重复代码
2. **一致性**: 确保两个列表页面的行为一致
3. **可维护性**: 修改列表功能只需要修改一个地方
4. **可扩展性**: 可以轻松添加新的列表页面
5. **类型安全**: 完整的 TypeScript 类型支持

## 保持的差异化功能
- MapList: 创建和导入按钮、付费检查、标签缓存失效
- TrashList: 清空回收站功能、不同的高度配置

## 测试建议
1. 验证 MapList 的创建、导入、搜索功能
2. 验证 TrashList 的清空、搜索功能
3. 测试移动端响应式布局
4. 测试分页功能
5. 测试空状态显示
