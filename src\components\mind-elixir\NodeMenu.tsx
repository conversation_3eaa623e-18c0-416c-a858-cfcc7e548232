import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '../ui/tabs'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { useMindElixirStore } from '@/store'
import GeneralNodeSettings from './GeneralNodeSettings'
import NoteEditor from './NoteEditor'
import ImageSelector from './ImageSelector'
import GlobalSettings from './GlobalSettings'
import { useTranslation } from 'react-i18next'

const NodeMenu = () => {
  const { t } = useTranslation()
  const node = useMindElixirStore(state => state.currentNode)
  const nodeMenuCollapsed = useMindElixirStore(state => state.nodeMenuCollapsed)
  const toggleNodeMenuCollapsed = useMindElixirStore(state => state.toggleNodeMenuCollapsed)
  const mei = useMindElixirStore(state => state.mei)
  console.log('render NodeMenu, node: ', node, 'nodeMenuCollapsed: ', nodeMenuCollapsed, 'mei: ', mei)

  return (
    <div className="h-full flex">
      <div className="flex items-center cursor-pointer" onClick={toggleNodeMenuCollapsed}>
        {nodeMenuCollapsed ? <ChevronLeft size={16} /> : <ChevronRight size={16} />}
      </div>
      <div className={`h-full bg-background ${nodeMenuCollapsed ? 'w-0 overflow-hidden opacity-0' : 'w-64 opacity-100 py-2 pr-2'}`}>
        <Tabs defaultValue="general" className={'w-full h-full flex flex-col ' + (!node ? 'hidden' : '')}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="general">{t('nodeMenu.general')}</TabsTrigger>
            <TabsTrigger value="note">{t('nodeMenu.note')}</TabsTrigger>
            <TabsTrigger value="image">{t('nodeMenu.image')}</TabsTrigger>
          </TabsList>
          <TabsContent className="flex-1 overflow-hidden" value="general">
            <GeneralNodeSettings />
          </TabsContent>
          <TabsContent className="p-3 pt-1" value="note">
            <NoteEditor />
          </TabsContent>
          <TabsContent className="p-3 pt-1" value="image">
            <ImageSelector />
          </TabsContent>
        </Tabs>
        <Tabs defaultValue='global' className={'w-full h-full flex flex-col ' + (node ? 'hidden' : '')}>
          <TabsList className="grid w-full grid-cols-1">
            <TabsTrigger value="global">{t('nodeMenu.mindMapSettings')}</TabsTrigger>
          </TabsList>
          <TabsContent className="flex-1 overflow-hidden" value="global">
            <GlobalSettings />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

export default NodeMenu
