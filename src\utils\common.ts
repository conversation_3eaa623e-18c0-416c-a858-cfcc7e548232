export const formatTime = (timestamp: number) => {
  const date = new Date(timestamp)
  return date.toLocaleString('en-US', {
    hour: 'numeric',
    minute: 'numeric',
    day: 'numeric',
    month: 'short',
    year: 'numeric',
    hour12: true,
  })
}

export function generateFileId(): string {
  return +new Date() + Math.random().toString(36).substring(2)
}

export function generateUUID(): string {
  return (new Date().getTime().toString(16) + Math.random().toString(16).substr(2)).substr(2, 16)
}

export function sleep(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

export const isDev = import.meta.env.TAURI_ENV_DEBUG
