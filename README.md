## Roadmap

- 关联另一个思维导图，一个图点到另一个图
- Stronghold 私密储存
- 双击 me 文件打开思维导图
- 按 tag 高亮当前图所有同 tag 节点（或其他节点降低透明度）
- 新增 tag 添加教程，引导用户使用 tag 功能
- 右键菜单添加递归展开递归收缩
- 加一些可以生成图片让用户晒的功能，署名可标记社交账号
- 加 always on top 
- ai运行中边缘发光
- core: 自由节点

## 怪异现象

- tauri 提供的 `fetch`，开发环境必须设置代理才能链接 cloud.mind-elixir.com（后端 log 直接看不到到达），但实际上它也不需要代理
- 生产环境直连也是不行，配代理会报 `Uncaught (in promise) SyntaxError: Failed to execute 'close' on 'ReadableStreamDefaultController': Unexpected end of JSON input`
- 不开启代理开发和生产报的都是 `Uncaught (in promise) error sending request for url (https://mind-elixir-backend.fly.dev/api/public?pageSize=10&page=1&name=)`
- 确认直接用浏览器原生 `fetch` 是可以直连 cloud.mind-elixir.com 的

## debug 过程

- devtool 看到的 preflight 是无法避免的，跟 rust 后端交流也必须有 preflight，这跟真实请求无关
- 已经发现生产是返回 403
- 应该是 tauri 插件里生产跟开发之间确实有逻辑差异
- 尝试本地调试看是哪里报的 403

## 初步解决

- 还真 tm 是 CORS 的问题，本来以为 tauri 的 fetch 不会带 origin，结果自动带上了 `Origin: http://tauri.localhost`，`gin-contrib/cors` 检测到没配直接返回 403
- 但这只是在公司的网络下，好像我加的网络不是报 403 而是真正的联不通，参考上面提到的后端根本没收到 log
- **确定了，我家的网真的好奇怪，我复制链接可以直连，但是用 tauri fetch 就是不行**，不知道 tauri fetch 搞了什么黑魔法
- 在下一步我试了一下连本地，无敌了，`http://localhost:7001/api/public?pageSize=10&page=1&name=` 都联不通，无敌了我家的网
- 之前 trae 死命联不通我就感觉我的网是有问题的，可能不知道哪里配了全局代理之类，但是找不到，这下好了，在自己产品复现了，无语
