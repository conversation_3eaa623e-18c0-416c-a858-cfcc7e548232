import { AlertCircle, Search } from 'lucide-react'
import { Input } from '../ui/input'
import { useEffect, useState } from 'react'
import { MindElixirLocal } from '@/api/map'
import TemplateCard from '../mind-elixir/TemplateCard'
import { <PERSON>ert, AlertTitle, AlertDescription } from '../ui/alert'
import Nowloading from '../NowLoading'
import { fetchSharedList } from '@/api/cloud'

const HubList = () => {
  const [isLoading, setIsLoading] = useState(false)
  const [maps, setMaps] = useState<MindElixirLocal[] | undefined>(undefined)
  const getList = async () => {
    setIsLoading(true)
    const data = await fetchSharedList()
    setMaps(data as MindElixirLocal[])
    setIsLoading(false)
  }
  useEffect(() => {
    getList()
  }, [])
  return (
    <div>
      {/* Search and New Map Button */}
      <div className="p-3 flex gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input type="search" placeholder="Search mind maps..." className="pl-8 h-8" />
        </div>
      </div>

      {/* Mind Maps Grid */}
      <div className="flex-1 overflow-auto p-3">
        {isLoading ? (
          <Nowloading />
        ) : maps ? (
          maps.length ? (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3">
              {/* Existing Mind Map Cards */}
              {maps.map((map, i) => (
                <TemplateCard key={i} map={map} onDone={getList} type="share" />
              ))}
            </div>
          ) : (
            <div className="flex items-center justify-center h-32 text-muted-foreground">
              <Alert className="max-w-md mx-auto mb-4">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>No Data</AlertTitle>
                <AlertDescription>The recyele bin is empty.</AlertDescription>
              </Alert>
            </div>
          )
        ) : null}
      </div>
    </div>
  )
}

export default HubList
