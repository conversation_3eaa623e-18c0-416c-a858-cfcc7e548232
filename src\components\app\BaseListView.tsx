import { Search } from 'lucide-react'
import { Input } from '../ui/input'
import { useCallback, useEffect, useState, ReactNode } from 'react'
import { useTranslation } from 'react-i18next'
import { MindElixirLocal, MindElixirLoadError, searchMaps } from '@/api/map'
import PreviewCard from '../mind-elixir/PreviewCard'
import CorruptedCard from '../mind-elixir/CorruptedCard'
import { ListPagination } from '../unofficial/list-pagination'
import { useSearchMapStore } from '@/store'
import { ScrollArea } from '../ui/scroll-area'

export interface BaseListViewConfig {
  // API 配置
  listApi: (page: number, size: number) => Promise<{ total: number; list: (MindElixirLocal | MindElixirLoadError)[] }>
  searchDir: string
  cardType: 'map' | 'trash'

  // UI 配置
  actionButtons?: ReactNode
  dialogs?: ReactNode
  emptyStateContent?: ReactNode

  // 回调函数
  onListChange?: (total: number) => void

  // 高度配置
  mobileHeightOffset?: number
  desktopHeightOffset?: number
  mobileMinHeightOffset?: number
  desktopMinHeightOffset?: number
}

interface BaseListViewProps {
  config: BaseListViewConfig
}

const BaseListView = ({ config }: BaseListViewProps) => {
  const [isLoading, setIsLoading] = useState(true)
  const { t } = useTranslation()
  const size = 12
  const [page, setPage] = useState(1)
  const [total, setTotal] = useState(0)
  const { searchMapList } = useSearchMapStore()
  const [maps, setMaps] = useState<(MindElixirLocal | MindElixirLoadError)[]>([])

  // 检测移动端
  const [isMobile, setIsMobile] = useState(false)
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768) // md断点
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)

    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  const getList = useCallback(async () => {
    // setIsLoading(true) // 有内容时 loading 不要隐藏内容，会造成闪屏
    const { list, total } = await config.listApi(page, size)
    if (list.length === 0 && page > 1) {
      setPage(page - 1)
      return
    }
    setMaps(list)
    setIsLoading(false)
    setTotal(total)

    // 调用回调函数
    config.onListChange?.(total)
  }, [page, config])

  useEffect(() => {
    getList()
  }, [getList])

  const [keyword, setKeyword] = useState('')
  const handleSearch = async (e: React.ChangeEvent<HTMLInputElement>) => {
    setKeyword(e.target.value)
    setIsLoading(true)
    await searchMaps(config.searchDir, 'topic', e.target.value)
    setIsLoading(false)
  }

  // 默认高度配置
  const mobileHeightOffset = config.mobileHeightOffset ?? 180
  const desktopHeightOffset = config.desktopHeightOffset ?? 56
  const mobileMinHeightOffset = config.mobileMinHeightOffset ?? 280
  const desktopMinHeightOffset = config.desktopMinHeightOffset ?? 140

  return (
    <div>
      {config.dialogs}
      <div className="p-3 flex flex-col sm:flex-row gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            type="search"
            placeholder={t('common.searchMindMap')}
            value={keyword}
            className="pl-8 h-8"
            onChange={handleSearch}
          />
        </div>
        {config.actionButtons}
      </div>
      {!isLoading && (
        <ScrollArea
          style={{
            height: isMobile ? `calc(100vh - ${mobileHeightOffset}px)` : `calc(100vh - ${desktopHeightOffset}px)`,
          }}
        >
          <div
            className="flex-1 overflow-auto p-3"
            style={{
              minHeight: isMobile ? `calc(100vh - ${mobileMinHeightOffset}px)` : `calc(100vh - ${desktopMinHeightOffset}px)`
            }}
          >
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
              {keyword ? (
                <>
                  {!searchMapList.length && <div>No results found</div>}
                  {searchMapList.map((map, i) => (
                    <PreviewCard key={i} map={map} onDone={getList} type={config.cardType} />
                  ))}
                </>
              ) : (
                <>
                  {maps.map((map, i) => (
                    'error' in map ? (
                      <CorruptedCard key={i} map={map} onDone={getList} type={config.cardType} />
                    ) : (
                      <PreviewCard key={i} map={map} onDone={getList} type={config.cardType} />
                    )
                  ))}
                  {!maps.length && config.emptyStateContent}
                </>
              )}
            </div>
            {!keyword && !maps.length && !config.emptyStateContent && (
              <div className="flex items-center justify-center h-32 text-muted-foreground">
                <div className="text-center">
                  <div className="text-sm">{t('common.noData')}</div>
                </div>
              </div>
            )}
          </div>
          {!keyword && (
            <div className={`sticky bottom-0 bg-background z-10 ${isMobile ? 'border-t border-border shadow-lg' : ''}`}>
              <ListPagination total={total} page={page} size={size} setPage={setPage} />
            </div>
          )}
        </ScrollArea>
      )}
    </div>
  )
}

export default BaseListView
