import { ClientOptions, fetch } from '@tauri-apps/plugin-http'
import { fetchEventSource, FetchEventSourceInit } from '@microsoft/fetch-event-source'
import { AiData } from '@/types/ai'
import { OpenAIStreamingResponse } from '@/types/openai'
import { generateUUID } from '@/utils/common'
import { useUserSettings } from '@/store'

type Resp = {
  resp: {
    data: {
      topic: string
    }[]
  }
}

export const aiReq = async ({ topic, prompt }: { topic: string; prompt: string }) => {
  const res = await fetch('/api/me-ai', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      topic,
      prompt,
    }),
  })
  return res.json() as Promise<Resp>
}

const generatePrompt = (topic: string, prompt?: string, context?: string) => `
Generate **at least 6 DISTINCT** and **highly relevant** sub-nodes (or sub-sub-nodes if necessary) that explore **different facets** of the given topic.

#### **Strict constraints:**  
- The response **MUST** be in the same language as user inputs.
- **Do NOT repeat the main topic** verbatim or with only minor rewording. Each sub-node must introduce a **new** and **specific** aspect.  
- If the topic is **a process**, structure sub-nodes as **ordered steps** with a logical progression.  
- If the topic is **conceptual**, cover **different dimensions**, such as historical context, key ideas, criticisms, influences, and applications.  
- If the topic is **a question**, first provide a **concise answer**, then generate sub-nodes that explore different **aspects of reasoning**.  
- **Prioritize diversity**: Each sub-node should contribute a **unique** insight rather than overlap with others.  

#### **Return format:**  
- **Output ONLY the sub-nodes** (NO topic repetition, NO extra text).  
- **Format:** \`id, parent id, text\`  
- **Example format:**  
  \`\`\`
  1,null, content of the first child node
  2,null, content of the second child node
  3,null, content of the third child node
  4,1, content of the first child node's child node
  ...
  \`\`\`  
- **No additional output**—return the sub-nodes **as is** without extra formatting, explanations, or introductory text.  

---

#### **User Inputs:**  

- **Topic:** ${topic}  
- **Additional Instructions (if any):** ${prompt || ''}  

${context ? `#### **Full Mind Map Context:**\n${context}` : ''}
`

export const aiReqStream = (topic: string, prompt?: string, context?: string) => {
  const promptText = generatePrompt(topic, prompt, context)
  console.log(promptText)
  const setting = useUserSettings.getState()
  const provider = setting.aiProvider

  if (provider === 'openai') {
    return openaiReqStream(promptText)
  } else if (provider === 'openai-compatible') {
    return openaiCompatibleReqStream(promptText)
  } else {
    return geminiReqStream(promptText)
  }
}

interface AiContent {
  content?: string
  reasoningContent?: string
}

// Common interface for stream request configuration
interface StreamRequestConfig {
  url: string
  headers: Record<string, string>
  body: string
  parseMessage: (ev: { data: string }) => AiContent | null
}

// Common function to handle stream requests
const streamRequest = (config: StreamRequestConfig) => {
  const setting = useUserSettings.getState()
  const proxy = setting.proxy
  const proxyEnabled = setting.proxyEnabled

  return new ReadableStream<AiContent>({
    start(controller) {
      fetchEventSource(config.url, {
        method: 'POST',
        headers: config.headers,
        body: config.body,
        fetch,
        proxy: proxyEnabled
          ? {
              all: proxy,
            }
          : undefined,
        onmessage(ev) {
          try {
            const content = config.parseMessage(ev)
            if (content) {
              controller.enqueue(content)
            }
          } catch (error) {
            console.error('Error parsing response:', error, ev.data)
          }
        },
        async onopen(response) {
          console.log('streamRequest open')
          console.log(response)
          if (response.ok) return
          const data = await response.text()
          controller.error(data || response.statusText || 'Unknown error')
        },
        onerror(err) {
          controller.error(err)
          throw err // prevent retry
        },
        onclose() {
          controller.close()
        },
      } as FetchEventSourceInit & ClientOptions)
    },
  })
}

const geminiReqStream = (promptText: string) => {
  const setting = useUserSettings.getState()
  const GEMINI_API_KEY = setting.geminiKey
  const model = setting.geminiModel
  return streamRequest({
    url: `https://generativelanguage.googleapis.com/v1beta/${model}:streamGenerateContent?alt=sse&key=${GEMINI_API_KEY}`,
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      contents: [
        {
          parts: [{ text: promptText }],
        },
      ],
    }),
    parseMessage: ev => {
      const data: AiData = JSON.parse(ev.data)
      console.log(data)
      return { content: data.candidates[0].content.parts[0].text }
    },
  })
}

const openaiCompatibleParser = (ev: { data: string }) => {
  if (ev.data === '[DONE]') {
    return { content: '\n' }
  }
  const data: OpenAIStreamingResponse = JSON.parse(ev.data)
  const delta = data.choices[0]?.delta
  return {
    content: delta?.content || '',
    reasoningContent: delta?.reasoning_content || '',
  }
}

export const listModel = (baseUrl: string, apiKey: string) => {
  const setting = useUserSettings.getState()
  const proxy = setting.proxy
  const proxyEnabled = setting.proxyEnabled
  return fetch(`${baseUrl}/models`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${apiKey}`,
    },
    proxy: proxyEnabled
      ? {
          all: proxy,
        }
      : undefined,
  })
}

const openaiReqStream = (promptText: string) => {
  const setting = useUserSettings.getState()
  const OPENAI_API_KEY = setting.openaiKey
  const model = setting.openaiModel
  return streamRequest({
    url: `https://api.openai.com/v1/chat/completions`,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${OPENAI_API_KEY}`,
    },
    body: JSON.stringify({
      model,
      messages: [
        {
          role: 'user',
          content: promptText,
        },
      ],
      stream: true,
    }),
    parseMessage: openaiCompatibleParser,
  })
}

const openaiCompatibleReqStream = (promptText: string) => {
  const setting = useUserSettings.getState()
  const OPENAI_API_KEY = setting.openaiKey
  // const baseUrl = 'api.chatanywhere.tech'
  const baseUrl = setting.baseUrl
  const model = setting.openaiCompatibleModel
  return streamRequest({
    url: `${baseUrl}/chat/completions`,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${OPENAI_API_KEY}`,
    },
    body: JSON.stringify({
      model,
      messages: [
        {
          role: 'user',
          content: promptText,
        },
      ],
      stream: true,
    }),
    parseMessage: openaiCompatibleParser,
  })
}

interface NodeAction {
  type: 'addChild' | 'editTopic'
  payload: { id: string; pid?: string; topic?: string }
}

interface ThinkingStartAction {
  type: 'thinkingStart'
}

interface ThinkingEndAction {
  type: 'thinkingEnd'
}

interface ThinkingAction {
  type: 'thinking'
  payload: {
    content: string
  }
}

export type Action = NodeAction | ThinkingAction | ThinkingStartAction | ThinkingEndAction

export class MindMapTransformStream extends TransformStream<AiContent, Action> {
  buffer: string
  currentNode: { id: string; topic: string } | null
  idMap: Record<string, string>
  isThinking: boolean
  constructor() {
    super({
      transform: (chunk, controller) => {
        if (chunk.reasoningContent) {
          // If this is the first reasoning chunk, emit thinkingStart
          if (!this.isThinking) {
            controller.enqueue({ type: 'thinkingStart' })
            this.isThinking = true
          }

          controller.enqueue({
            type: 'thinking',
            payload: { content: chunk.reasoningContent || '' },
          })
        } else {
          // If we were thinking and now received content, ensure thinking is ended
          if (this.isThinking) {
            controller.enqueue({ type: 'thinkingEnd' })
            this.isThinking = false
          }
          this.processChunk(chunk.content || '', controller)
        }
      },
    })
    this.buffer = ''
    this.currentNode = null
    this.idMap = {}
    this.isThinking = false
  }

  processChunk(chunk: string, controller: TransformStreamDefaultController<Action>) {
    this.buffer += chunk
    const lines = this.buffer.split('\n')

    // 保留未完成的一行
    this.buffer = lines.pop() || ''

    for (const line of lines) {
      this.processLine(line, controller)
    }
  }

  processLine(line: string, controller: TransformStreamDefaultController<Action>) {
    // Use array destructuring to ignore the first element
    const [, ouid, parent, topic] = line.match(/^([^,]+),([^,]+),(.+)$/) || []
    if (!ouid || !parent) return

    const uid = this.idMap[ouid] || (this.idMap[ouid] = generateUUID())

    if (!this.currentNode || this.currentNode.id !== uid) {
      controller.enqueue({ type: 'addChild', payload: { id: uid, pid: parent } })
      this.currentNode = { id: uid, topic: '' }
    }

    this.currentNode.topic += topic
    controller.enqueue({ type: 'editTopic', payload: { id: uid, topic: this.currentNode.topic } })
  }

  getStream() {
    return this.readable
  }
}
