import NodeMenu from '@/components/mind-elixir/NodeMenu'
import ContextMenu from '@/components/mind-elixir/ContextMenu'
import MobileActionBar from '@/components/mind-elixir/MobileActionBar'
import { createTips } from '@/utils/mind-elixir'
import MindElixir, { MindElixirData, MindElixirInstance, NodeObj } from 'mind-elixir'
import { useEffect, useRef, useState } from 'react'
import ToolButtonGroup from '@/components/mind-elixir/ToolButtonGroup'
import { useTheme } from '@/components/theme-provider'
import TopMenu from '@/components/mind-elixir/TopMenu'
import MiniMap from '@/components/mind-elixir/MiniMap'
import { useMindElixirStore } from '@/store'
import { loadMap, loadTemplate, MindElixirLocal } from '@/api/map'
import { listen, emit } from '@tauri-apps/api/event'
import { generateUUID, sleep } from '@/utils/common'
import { Arrow } from 'node_modules/mind-elixir/dist/types/arrow'
import { Summary } from 'node_modules/mind-elixir/dist/types/summary'
import { OutlinerMode } from '@/components/app/OutlinerMode'
import debounce from '@/utils/debounce'
import SearchBar from '@/components/mind-elixir/SearchBar'
import ShortcutsDialog from '@/components/mind-elixir/ShortcutsDialog'
import { JsonInsertDialog } from '@/components/mind-elixir/JsonInsertDialog'
import { useTranslation } from 'react-i18next'
import { toast } from 'sonner'
import { isMobile } from '@/utils/platform'

type MindmapEvent =
  | {
      event: 'getAllNodes'
      data: never
    }
  | {
      event: 'generateMindmap'
      data: { mindmapData: string }
    }
  | {
      event: 'addChild'
      data: { parentId: string; topic: string }
    }
  | {
      event: 'editTopic'
      data: { nodeId: string; topic: string }
    }
  | {
      event: 'createSummary'
      data: Omit<Summary, 'id'>
    }
  | {
      event: 'createArrow'
      data: Omit<Arrow, 'id' | 'delta1' | 'delta2'>
    }

const E = MindElixir.E

const store = useMindElixirStore.getState()
const MindmapEdit = () => {
  const { t } = useTranslation()
  const context = useTheme()
  const [mode, setMode] = useState<'map' | 'outline'>('map')
  const meiRef = useRef<MindElixirInstance>(undefined)
  const elRef = useRef<HTMLDivElement>(null)
  const [outlinerData, setOutlinerData] = useState<NodeObj[]>([])
  const [isUnsaved, setIsUnsaved] = useState(false)

  // Use store for search dialog state and node menu state
  const searchOpen = useMindElixirStore(state => state.searchOpen)
  const setSearchOpen = useMindElixirStore(state => state.setSearchOpen)
  const toggleNodeMenuCollapsed = useMindElixirStore(state => state.toggleNodeMenuCollapsed)

  // Shortcuts dialog state
  const [shortcutsDialogOpen, setShortcutsDialogOpen] = useState(false)
  const hasSeenShortcutsDialog = useMindElixirStore(state => state.hasSeenShortcutsDialog)
  const setHasSeenShortcutsDialog = useMindElixirStore(state => state.setHasSeenShortcutsDialog)

  // JSON insert dialog state
  const [jsonInsertDialogOpen, setJsonInsertDialogOpen] = useState(false)

  // Handle JSON insert
  const handleJsonInsert = (data: NodeObj | NodeObj[], isNodeData: boolean) => {
    const mei = meiRef.current
    if (!mei || mei.currentNodes.length === 0) {
      toast.error(t('error.noNodeSelected'))
      return
    }

    const currentNode = mei.currentNodes[0]

    try {
      if (isNodeData) {
        // 替换当前节点的数据
        const nodeData = data as NodeObj
        const currentObj = currentNode.nodeObj
        currentObj.topic = nodeData.topic
        currentObj.children = nodeData.children
        mei.refresh()
      } else {
        const children = data as NodeObj[]
        // 替换当前节点的子节点
        currentNode.nodeObj.children = children
        mei.refresh()
      }

      setIsUnsaved(true)
    } catch (error) {
      console.error('Error inserting JSON:', error)
      toast.error(t('error.jsonInsertFailed'))
    }
  }

  useEffect(() => {
    const unlisten = listen<MindmapEvent>('operation', async e => {
      console.log(e)
      await sleep(200)
      const payload = e.payload
      if (payload.event === 'getAllNodes') {
        emit('success', meiRef.current?.getData()?.nodeData)
      } else if (payload.event === 'generateMindmap') {
        const data = JSON.parse(payload.data.mindmapData) as MindElixirData
        meiRef.current?.refresh(data)
        emit('success', 'OK')
      } else if (payload.event === 'addChild') {
        const { parentId, topic } = payload.data
        const tpc = E(parentId)
        if (tpc) {
          const pid = generateUUID()
          await meiRef.current?.addChild(tpc, {
            id: pid,
            topic,
          })
          emit('success', `Done. New Node's ID is ${pid}.`)
        } else {
          emit('error', 'Parent node not found')
        }
      } else if (payload.event === 'editTopic') {
        const { nodeId, topic } = payload.data
        meiRef.current?.setNodeTopic(E(nodeId), topic)
        emit('success', 'OK')
      } else if (payload.event === 'createSummary') {
        meiRef.current?.createSummaryFrom(payload.data)
        emit('success', 'OK')
      } else if (payload.event === 'createArrow') {
        const data = payload.data
        meiRef.current?.createArrowFrom({
          ...data,
          delta1: {
            x: E(data.from).offsetWidth / 2 + 100,
            y: 0,
          },
          delta2: {
            x: E(data.to).offsetWidth / 2 + 100,
            y: 0,
          },
        })
        emit('success', 'OK')
      }
    })
    return () => {
      unlisten.then(unlisten => {
        unlisten()
      })
    }
  }, [])

  useEffect(() => {
    const params = new URLSearchParams(window.location.search)
    const id = params.get('id')!
    const template = params.get('template')
    // TODO: 优化白屏闪了一下
    // TODO: 高级功能，插入json，选择某个节点，按 ctrl J，粘贴 json，如果是 nodeData 就直接置换当前节点，是 children[] 就添加子节点
    let theme = context.theme
    if (context.theme === 'system') theme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
    const mei = new MindElixir({
      theme: theme === 'dark' ? MindElixir.DARK_THEME : MindElixir.THEME,
      el: elRef.current!,
      contextMenu: false,
      toolBar: false,
      allowUndo: true,
    })
    const handleNodeChange = () => {
      const nodes = mei.currentNodes
      store.setCurrentNodes(nodes.map(node => node.nodeObj))
    }
    const debounceNodeChange = debounce(handleNodeChange, 200)
    const handleContextMenu = (e: MouseEvent) => {
      if (mei.currentNodes.length < 1) {
        return
      }
      const fake = new MouseEvent('contextmenu', e)
      document.querySelector('.context-menu-target')?.dispatchEvent(fake)
    }

    mei.bus.addListener('selectNode', debounceNodeChange)
    mei.bus.addListener('selectNodes', debounceNodeChange)
    mei.bus.addListener('unselectNodes', debounceNodeChange)
    mei.bus.addListener('selectNewNode', debounceNodeChange)
    mei.bus.addListener('linkDiv', debounceNodeChange) // 调用一些修改节点的方法，如 reshapeNode 时，会触发这个事件
    mei.bus.addListener('showContextMenu', handleContextMenu)

    meiRef.current = mei
    store.setMei(mei)
    const controller = new AbortController()
    const init = async () => {
      let data: MindElixirLocal | null
      if (template) {
        data = await loadTemplate(id!, template)
      } else {
        const map = await loadMap('maps', id!)
        // 新建的时候还真是null
        if (map !== null && 'error' in map) return
        data = map
      }
      if (controller.signal.aborted) return
      if (data) {
        store.setFile(data)
        mei.init(data.content)
      } else {
        store.setFile(undefined)
        mei.init(MindElixir.new(t('mindmap.newMindMap')))
      }
      console.log(mei, 'mei')
    }
    init()

    const tips = createTips(t('tips.pressF1ToCenter'))

    const observer = new IntersectionObserver(entries => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          tips.remove()
        } else {
          mei.container.appendChild(tips)
        }
      })
    })
    observer.observe(mei.nodes)

    // Add keyboard shortcuts
    const handleKeyDown = (e: KeyboardEvent) => {
      // 搜索快捷键 (Ctrl+F)
      if (e.key === 'f' && (e.ctrlKey || e.metaKey)) {
        e.preventDefault()
        setSearchOpen(true)
      }

      // 节点菜单收缩展开快捷键 (Ctrl+M)
      if (e.key === 'm' && (e.ctrlKey || e.metaKey)) {
        e.preventDefault()
        toggleNodeMenuCollapsed()
      }

      // 快捷键弹窗快捷键 (?)
      if (e.key === '?') {
        e.preventDefault()
        setShortcutsDialogOpen(true)
      }

      // JSON 插入快捷键 (Ctrl+J)
      if (e.key === 'j' && (e.ctrlKey || e.metaKey)) {
        e.preventDefault()
        // 检查是否有选中的节点
        if (mei.currentNodes.length === 0) {
          toast.error(t('error.noNodeSelected'))
          return
        }
        setJsonInsertDialogOpen(true)
      }
    }

    document.addEventListener('keydown', handleKeyDown)

    return () => {
      controller.abort()
      mei.bus.removeListener('selectNode', debounceNodeChange)
      mei.bus.removeListener('selectNodes', debounceNodeChange)
      mei.bus.removeListener('unselectNodes', debounceNodeChange)
      mei.bus.removeListener('selectNewNode', debounceNodeChange)
      mei.bus.removeListener('linkDiv', debounceNodeChange)
      mei.bus.removeListener('showContextMenu', handleContextMenu)
      document.removeEventListener('keydown', handleKeyDown)
      mei.destroy()
      store.setMei(undefined)
      store.setCurrentNodes([])
      observer.disconnect()
    }
  }, [context.theme, setSearchOpen, toggleNodeMenuCollapsed, t])

  // Show shortcuts dialog on first visit
  useEffect(() => {
    if (!hasSeenShortcutsDialog) {
      // Short delay to ensure the component is fully mounted
      const timer = setTimeout(() => {
        setShortcutsDialogOpen(true)
        setHasSeenShortcutsDialog(true)
      }, 500)

      return () => clearTimeout(timer)
    }
  }, [hasSeenShortcutsDialog, setHasSeenShortcutsDialog])

  return (
    <div className="relative h-screen h-[100dvh]">
      <TopMenu
        mode={mode}
        setMode={setMode}
        outlinerData={outlinerData}
        setOutlinerData={setOutlinerData}
        isUnsaved={isUnsaved}
        setIsUnsaved={setIsUnsaved}
      />
      <div className="relative h-full w-full flex">
        <ContextMenu>
          <div ref={elRef} id="map" className="h-full"></div>
        </ContextMenu>
        <MiniMap />
        <ToolButtonGroup />
        {!isMobile() && <NodeMenu />}
      </div>
      {mode === 'outline' && (
        <OutlinerMode
          mei={meiRef.current}
          outlinerData={outlinerData}
          setOutlinerData={value => {
            setOutlinerData(value)
            setIsUnsaved(true)
          }}
        />
      )}
      <SearchBar open={searchOpen} setOpen={setSearchOpen} mei={meiRef.current} />
      <ShortcutsDialog open={shortcutsDialogOpen} setOpen={setShortcutsDialogOpen} showHint={!hasSeenShortcutsDialog} />
      <JsonInsertDialog open={jsonInsertDialogOpen} setOpen={setJsonInsertDialogOpen} onInsert={handleJsonInsert} />
      {isMobile() && <MobileActionBar />}
    </div>
  )
}

export default MindmapEdit
