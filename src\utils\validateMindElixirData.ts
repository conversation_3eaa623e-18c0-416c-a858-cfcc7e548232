/**
 * MindElixir 数据格式验证工具
 * 用于验证用户提供的数据是否符合 MindElixirData 类型规范
 */

/**
 * 验证数据是否符合 MindElixirData 类型
 * @param data 要验证的数据
 * @returns 验证结果，包含是否有效和错误信息
 */
export const validateMindElixirData = (data: unknown): { isValid: boolean; error?: string } => {
  // 检查数据是否为对象
  if (!data || typeof data !== 'object') {
    return { isValid: false, error: '数据必须是一个对象' }
  }

  const dataObj = data as Record<string, unknown>

  // 检查必需的 nodeData 属性
  if (!dataObj.nodeData) {
    return { isValid: false, error: '缺少必需的 nodeData 属性' }
  }

  // 验证 nodeData 是否为有效的 NodeObj
  const nodeDataValidation = validateNodeObj(dataObj.nodeData)
  if (!nodeDataValidation.isValid) {
    return { isValid: false, error: `nodeData 无效: ${nodeDataValidation.error}` }
  }

  // 验证可选的 arrows 属性
  if (dataObj.arrows !== undefined) {
    if (!Array.isArray(dataObj.arrows)) {
      return { isValid: false, error: 'arrows 必须是数组' }
    }
    for (let i = 0; i < dataObj.arrows.length; i++) {
      const arrowValidation = validateArrow(dataObj.arrows[i])
      if (!arrowValidation.isValid) {
        return { isValid: false, error: `arrows[${i}] 无效: ${arrowValidation.error}` }
      }
    }
  }

  // 验证可选的 summaries 属性
  if (dataObj.summaries !== undefined) {
    if (!Array.isArray(dataObj.summaries)) {
      return { isValid: false, error: 'summaries 必须是数组' }
    }
    for (let i = 0; i < dataObj.summaries.length; i++) {
      const summaryValidation = validateSummary(dataObj.summaries[i])
      if (!summaryValidation.isValid) {
        return { isValid: false, error: `summaries[${i}] 无效: ${summaryValidation.error}` }
      }
    }
  }

  // 验证可选的 direction 属性
  if (dataObj.direction !== undefined && typeof dataObj.direction !== 'number') {
    return { isValid: false, error: 'direction 必须是数字' }
  }

  // 验证可选的 theme 属性
  if (dataObj.theme !== undefined && typeof dataObj.theme !== 'object') {
    return { isValid: false, error: 'theme 必须是对象' }
  }

  return { isValid: true }
}

/**
 * 验证 NodeObj 对象
 * @param node 要验证的节点对象
 * @returns 验证结果
 */
export const validateNodeObj = (node: unknown): { isValid: boolean; error?: string } => {
  if (!node || typeof node !== 'object') {
    return { isValid: false, error: '节点必须是一个对象' }
  }

  const nodeObj = node as Record<string, unknown>

  // 检查必需的 topic 属性
  if (!nodeObj.topic || typeof nodeObj.topic !== 'string') {
    return { isValid: false, error: '节点必须有 topic 属性且为字符串' }
  }

  // 检查必需的 id 属性
  if (!nodeObj.id || typeof nodeObj.id !== 'string') {
    return { isValid: false, error: '节点必须有 id 属性且为字符串' }
  }

  // 验证可选的 children 属性
  if (nodeObj.children !== undefined) {
    if (!Array.isArray(nodeObj.children)) {
      return { isValid: false, error: 'children 必须是数组' }
    }
    for (let i = 0; i < nodeObj.children.length; i++) {
      const childValidation = validateNodeObj(nodeObj.children[i])
      if (!childValidation.isValid) {
        return { isValid: false, error: `children[${i}] 无效: ${childValidation.error}` }
      }
    }
  }

  // 验证可选的 style 属性
  if (nodeObj.style !== undefined) {
    if (typeof nodeObj.style !== 'object' || nodeObj.style === null) {
      return { isValid: false, error: 'style 必须是对象' }
    }
    const styleValidation = validateNodeStyle(nodeObj.style)
    if (!styleValidation.isValid) {
      return { isValid: false, error: `style 无效: ${styleValidation.error}` }
    }
  }

  // 验证可选的 tags 属性
  if (nodeObj.tags !== undefined) {
    if (!Array.isArray(nodeObj.tags) || !nodeObj.tags.every((tag: unknown) => typeof tag === 'string')) {
      return { isValid: false, error: 'tags 必须是字符串数组' }
    }
  }

  // 验证可选的 icons 属性
  if (nodeObj.icons !== undefined) {
    if (!Array.isArray(nodeObj.icons) || !nodeObj.icons.every((icon: unknown) => typeof icon === 'string')) {
      return { isValid: false, error: 'icons 必须是字符串数组' }
    }
  }

  // 验证可选的 hyperLink 属性
  if (nodeObj.hyperLink !== undefined && typeof nodeObj.hyperLink !== 'string') {
    return { isValid: false, error: 'hyperLink 必须是字符串' }
  }

  // 验证可选的 expanded 属性
  if (nodeObj.expanded !== undefined && typeof nodeObj.expanded !== 'boolean') {
    return { isValid: false, error: 'expanded 必须是布尔值' }
  }

  // 验证可选的 direction 属性
  if (nodeObj.direction !== undefined && typeof nodeObj.direction !== 'number') {
    return { isValid: false, error: 'direction 必须是数字' }
  }

  // 验证可选的 branchColor 属性
  if (nodeObj.branchColor !== undefined && typeof nodeObj.branchColor !== 'string') {
    return { isValid: false, error: 'branchColor 必须是字符串' }
  }

  // 验证可选的 note 属性
  if (nodeObj.note !== undefined && typeof nodeObj.note !== 'string') {
    return { isValid: false, error: 'note 必须是字符串' }
  }

  // 验证可选的 image 属性
  if (nodeObj.image !== undefined) {
    const imageValidation = validateNodeImage(nodeObj.image)
    if (!imageValidation.isValid) {
      return { isValid: false, error: `image 无效: ${imageValidation.error}` }
    }
  }

  return { isValid: true }
}

/**
 * 验证节点样式对象
 */
const validateNodeStyle = (style: unknown): { isValid: boolean; error?: string } => {
  if (!style || typeof style !== 'object') {
    return { isValid: false, error: '样式必须是对象' }
  }

  const styleObj = style as Record<string, unknown>

  // 验证可选的样式属性
  const validStyleProps = ['fontSize', 'color', 'background', 'fontWeight']
  for (const prop in styleObj) {
    if (!validStyleProps.includes(prop)) {
      continue // 允许未知属性，保持向前兼容
    }
    if (typeof styleObj[prop] !== 'string') {
      return { isValid: false, error: `样式属性 ${prop} 必须是字符串` }
    }
  }

  return { isValid: true }
}

/**
 * 验证节点图片对象
 */
const validateNodeImage = (image: unknown): { isValid: boolean; error?: string } => {
  if (!image || typeof image !== 'object') {
    return { isValid: false, error: '图片必须是对象' }
  }

  const imageObj = image as Record<string, unknown>

  // 检查必需属性
  if (typeof imageObj.url !== 'string') {
    return { isValid: false, error: '图片必须有 url 属性且为字符串' }
  }

  if (typeof imageObj.width !== 'number') {
    return { isValid: false, error: '图片必须有 width 属性且为数字' }
  }

  if (typeof imageObj.height !== 'number') {
    return { isValid: false, error: '图片必须有 height 属性且为数字' }
  }

  // 验证可选的 fit 属性
  if (imageObj.fit !== undefined) {
    const validFitValues = ['fill', 'contain', 'cover']
    if (!validFitValues.includes(imageObj.fit as string)) {
      return { isValid: false, error: 'fit 属性必须是 fill、contain 或 cover 之一' }
    }
  }

  return { isValid: true }
}

/**
 * 验证 Arrow 对象
 * @param arrow 要验证的箭头对象
 * @returns 验证结果
 */
export const validateArrow = (arrow: unknown): { isValid: boolean; error?: string } => {
  if (!arrow || typeof arrow !== 'object') {
    return { isValid: false, error: '箭头必须是一个对象' }
  }

  const arrowObj = arrow as Record<string, unknown>
  const requiredFields = ['id', 'label', 'from', 'to', 'delta1', 'delta2']
  
  for (const field of requiredFields) {
    if (arrowObj[field] === undefined) {
      return { isValid: false, error: `箭头缺少必需的 ${field} 属性` }
    }
  }

  if (typeof arrowObj.id !== 'string' || typeof arrowObj.label !== 'string' ||
      typeof arrowObj.from !== 'string' || typeof arrowObj.to !== 'string') {
    return { isValid: false, error: '箭头的 id、label、from、to 必须是字符串' }
  }

  // 验证 delta1
  const delta1 = arrowObj.delta1 as Record<string, unknown>
  if (!arrowObj.delta1 || typeof arrowObj.delta1 !== 'object' ||
      typeof delta1.x !== 'number' || typeof delta1.y !== 'number') {
    return { isValid: false, error: 'delta1 必须是包含 x、y 数字属性的对象' }
  }

  // 验证 delta2
  const delta2 = arrowObj.delta2 as Record<string, unknown>
  if (!arrowObj.delta2 || typeof arrowObj.delta2 !== 'object' ||
      typeof delta2.x !== 'number' || typeof delta2.y !== 'number') {
    return { isValid: false, error: 'delta2 必须是包含 x、y 数字属性的对象' }
  }

  // 验证可选的 bidirectional 属性
  if (arrowObj.bidirectional !== undefined && typeof arrowObj.bidirectional !== 'boolean') {
    return { isValid: false, error: 'bidirectional 必须是布尔值' }
  }

  return { isValid: true }
}

/**
 * 验证 Summary 对象
 * @param summary 要验证的摘要对象
 * @returns 验证结果
 */
export const validateSummary = (summary: unknown): { isValid: boolean; error?: string } => {
  if (!summary || typeof summary !== 'object') {
    return { isValid: false, error: '摘要必须是一个对象' }
  }

  const summaryObj = summary as Record<string, unknown>
  const requiredFields = ['id', 'label', 'parent', 'start', 'end']
  
  for (const field of requiredFields) {
    if (summaryObj[field] === undefined) {
      return { isValid: false, error: `摘要缺少必需的 ${field} 属性` }
    }
  }

  if (typeof summaryObj.id !== 'string' || typeof summaryObj.label !== 'string' ||
      typeof summaryObj.parent !== 'string') {
    return { isValid: false, error: '摘要的 id、label、parent 必须是字符串' }
  }

  if (typeof summaryObj.start !== 'number' || typeof summaryObj.end !== 'number') {
    return { isValid: false, error: '摘要的 start、end 必须是数字' }
  }

  // 验证 start 和 end 的逻辑关系
  if (summaryObj.start < 0 || summaryObj.end < 0) {
    return { isValid: false, error: 'start 和 end 必须是非负数' }
  }

  if (summaryObj.start > summaryObj.end) {
    return { isValid: false, error: 'start 不能大于 end' }
  }

  return { isValid: true }
}
