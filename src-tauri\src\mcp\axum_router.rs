use super::common::mindmap::Mindmap;
use axum::extract::{<PERSON><PERSON>, <PERSON><PERSON>};
use axum::http::HeaderMap;
use axum::response::{IntoResponse, Response};
use rmcp::transport::sse_server::{SseServer, SseServerConfig};
use serde::{Deserialize, Serialize};

use tauri::{App<PERSON><PERSON>le, Emitter};
const BIND_ADDRESS: &str = "127.0.0.1:6595";
const ALLOWED_ORIGIN: &str = "https://cloud.mind-elixir.com";

#[derive(Debug, Deserialize)]
struct Params {
    token: String,
}

#[derive(Debug, Deserialize)]
struct CreateMindmapRequest {
    mindmap: String,
    source: String,
}
#[derive(Clone, Serialize)]
#[serde(rename_all = "camelCase")]
struct Login {
    token: String,
}

#[derive(<PERSON>lone, Serialize)]
#[serde(rename_all = "camelCase")]
struct CreateMindmap {
    mindmap: String,
    source: String,
}

pub async fn serve(handle: AppHandle) -> anyhow::Result<()> {
    let config = SseServerConfig {
        bind: BIND_ADDRESS.parse()?,
        sse_path: "/sse".to_string(),
        post_path: "/message".to_string(),
        ct: tokio_util::sync::CancellationToken::new(),
        sse_keep_alive: None,
    };

    let (sse_server, mut router) = SseServer::new(config);
    let handle_clone = handle.clone();
    let handle_clone2 = handle.clone();

    // Create a handler that returns a response with CORS headers
    async fn login_handler(
        headers: HeaderMap,
        Query(params): Query<Params>,
        handle_clone: tauri::AppHandle,
    ) -> impl IntoResponse {
        tracing::info!("login handler called");
        let token = params.token;
        let _ = handle_clone.emit("login", Login { token: token });

        // Get the origin from request headers, fallback to default if not present
        let origin = headers
            .get("origin")
            .and_then(|h| h.to_str().ok())
            .unwrap_or(ALLOWED_ORIGIN);

        // Return the response with CORS headers
        Response::builder()
            .header("Access-Control-Allow-Origin", origin)
            .header("Access-Control-Allow-Methods", "POST, OPTIONS")
            .header(
                "Access-Control-Allow-Headers",
                "Content-Type, Authorization, Cookie",
            )
            .header("Access-Control-Allow-Credentials", "true")
            .body("OK".to_string())
            .unwrap()
    }

    // Create a handler for create-mindmap endpoint
    async fn create_mindmap_handler(
        headers: HeaderMap,
        Json(payload): Json<CreateMindmapRequest>,
        handle_clone: tauri::AppHandle,
    ) -> impl IntoResponse {
        tracing::info!("create-mindmap handler called");
        let _ = handle_clone.emit("create-mindmap", CreateMindmap { mindmap: payload.mindmap, source: payload.source });

        // Get the origin from request headers, fallback to default if not present
        let origin = headers
            .get("origin")
            .and_then(|h| h.to_str().ok())
            .unwrap_or(ALLOWED_ORIGIN);

        // Return the response with CORS headers
        Response::builder()
            .header("Access-Control-Allow-Origin", origin)
            .header("Access-Control-Allow-Methods", "POST, OPTIONS")
            .header(
                "Access-Control-Allow-Headers",
                "Content-Type, Authorization, Cookie",
            )
            .header("Access-Control-Allow-Credentials", "true")
            .body("OK".to_string())
            .unwrap()
    }

    // Create an OPTIONS handler for preflight requests
    async fn options_handler(headers: HeaderMap) -> impl IntoResponse {
        // Get the origin from request headers, fallback to default if not present
        let origin = headers
            .get("origin")
            .and_then(|h| h.to_str().ok())
            .unwrap_or(ALLOWED_ORIGIN);

        // For OPTIONS requests (preflight), we need to respond with the appropriate CORS headers
        Response::builder()
            .header("Access-Control-Allow-Origin", origin)
            .header("Access-Control-Allow-Methods", "POST, OPTIONS")
            .header(
                "Access-Control-Allow-Headers",
                "Content-Type, Authorization, Cookie",
            )
            .header("Access-Control-Allow-Credentials", "true")
            .body(String::new())
            .unwrap()
    }

    // Create a ping handler to check if service is running
    async fn ping_handler(headers: HeaderMap) -> impl IntoResponse {
        tracing::info!("ping handler called");
        
        // Get the origin from request headers, fallback to default if not present
        let origin = headers
            .get("origin")
            .and_then(|h| h.to_str().ok())
            .unwrap_or(ALLOWED_ORIGIN);
        // 打印 origin
        tracing::info!("origin: {}", origin);
        
        Response::builder()
            .header("Access-Control-Allow-Origin", origin)
            .header("Access-Control-Allow-Methods", "GET, OPTIONS")
            .header(
                "Access-Control-Allow-Headers",
                "Content-Type, Authorization, Cookie",
            )
            .header("Access-Control-Allow-Credentials", "true")
            .body("pong".to_string())
            .unwrap()
    }

    // Add routes for the login endpoint
    router = router
        .route(
            "/login",
            axum::routing::post(move |headers, query| login_handler(headers, query, handle_clone)),
        )
        .route("/login", axum::routing::options(options_handler))
        .route(
            "/create-mindmap",
            axum::routing::post(move |headers, payload| create_mindmap_handler(headers, payload, handle_clone2)),
        )
        .route("/create-mindmap", axum::routing::options(options_handler))
        .route("/ping", axum::routing::get(ping_handler))
        .route("/ping", axum::routing::options(options_handler));

    let listener = tokio::net::TcpListener::bind(sse_server.config.bind).await?;

    let ct = sse_server.config.ct.child_token();

    let server = axum::serve(listener, router).with_graceful_shutdown(async move {
        ct.cancelled().await;
        tracing::info!("sse server cancelled");
    });

    tokio::spawn(async move {
        if let Err(e) = server.await {
            tracing::error!(error = %e, "sse server shutdown with error");
        }
    });

    // let mindmap = Mindmap::new(window.clone());
    let mindmap = sse_server.with_service(move || Mindmap::new(handle.clone()));
    // let ct = sse_server.with_service(Counter::new);

    tokio::signal::ctrl_c().await?;
    // ct.cancel();
    mindmap.cancel();
    Ok(())
}
