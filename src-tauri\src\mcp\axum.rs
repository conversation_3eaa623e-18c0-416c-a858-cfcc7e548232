use common::counter::Counter;
use rmcp::transport::sse_server::SseServer;
use tracing_subscriber::{self};

use super::common;

const BIND_ADDRESS: &str = "127.0.0.1:8000";

pub async fn serve() -> anyhow::Result<()> {
    // tracing_subscriber::registry()
    //     .with(
    //         tracing_subscriber::EnvFilter::try_from_default_env()
    //             .unwrap_or_else(|_| "debug".to_string().into()),
    //     )
    //     .with(tracing_subscriber::fmt::layer())
    //     .init();

    let ct = SseServer::serve(BIND_ADDRESS.parse()?)
        .await?
        .with_service(Counter::new);

    tokio::signal::ctrl_c().await?;
    ct.cancel();
    Ok(())
}
