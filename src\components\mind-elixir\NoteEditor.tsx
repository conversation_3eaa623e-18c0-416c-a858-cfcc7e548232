import { Textarea } from '../ui/textarea'
import { useMindElixirStore } from '@/store'
import debounce from '@/utils/debounce'

const NoteEditor = () => {
  const node = useMindElixirStore(state => state.currentNode)
  const mei = useMindElixirStore(state => state.mei)

  const updateNote = (value: string) => {
    if (!node || !mei) return
    node.note = value
  }

  const onNoteChange = debounce(updateNote, 200)

  return (
    <Textarea
      rows={20}
      value={node?.note}
      onChange={e => {
        onNoteChange(e.target.value)
      }}
    />
  )
}

export default NoteEditor 