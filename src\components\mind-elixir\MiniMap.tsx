import { domToBlob } from '@ssshooter/modern-screenshot'
import { MindElixirInstance } from 'mind-elixir'
import {  useEffect, useRef, useState } from 'react'
import workerUrl from '@ssshooter/modern-screenshot/worker?url'
import debounce from '@/utils/debounce'
import { useMindElixirStore } from '@/store'
import { on } from '@/utils/on'

const getSvg = async (mei: MindElixirInstance) => {
  console.log('getSvg, this is a very expensive operation')
  // TODO 加载速度感觉还可以优化一下下
  const svg = await domToBlob(mei.nodes, {
    quality: 0.1,
    onCloneNode: node => {
      const n = node as HTMLDivElement
      n.style.position = ''
      n.style.top = ''
      n.style.left = ''
      n.style.bottom = ''
      n.style.right = ''
    },
    filter: node => {
      const el = node as HTMLElement
      return el.tagName !== 'SPAN' && el.tagName !== 'ME-EPD' && el.tagName !== 'IMG' && el.id !== 'input-box'
    },
    workerUrl: workerUrl,
    workerNumber: 1,
    width: mei.nodes.offsetWidth,
    height: mei.nodes.offsetHeight,
  })
  return svg
}

const MiniMap = ({ mei }: { mei: MindElixirInstance }) => {
  console.log('MiniMap', mei)
  const [imgUrl, setImgUrl] = useState<string | undefined>(undefined)
  const divRef = useRef<HTMLDivElement>(null)
  const imgRef = useRef<HTMLImageElement>(null)
  const ratioRef = useRef(1)

  const useMiniMap = useMindElixirStore(state => state.useMiniMap)

  useEffect(() => {
    if (!useMiniMap) return
    const updateSvg = debounce(async function updateSvg() {
      // 更新缩略图
      const blob = await getSvg(mei)
      const url = URL.createObjectURL(blob)
      console.log(url, 'imgUrl')
      setImgUrl(url)
    }, 500)
    const updateMiniMap = debounce(async () => {
      // 调整定位
      const { x, y, width, height } = mei.nodes.getBoundingClientRect()
      const { height: screenHeight, width: screenWidth } = mei.container.getBoundingClientRect()
      let screenDivWidth = 0
      let screenDivHeight = 0
      let top = 0
      let left = 0
      // 240 is the width of the minimap
      if (width > height) {
        const ratio = 240 / width
        ratioRef.current = ratio
        screenDivWidth = ratio * screenWidth
        screenDivHeight = ratio * screenHeight
        const mindMapHeight = height * ratio
        const heightOffset = (240 - mindMapHeight) / 2
        top = heightOffset - ratio * y
        left = -ratio * x
      } else {
        const ratio = 240 / height
        ratioRef.current = ratio
        screenDivHeight = ratio * screenHeight
        screenDivWidth = ratio * screenWidth
        const mindMapWidth = ratio * width
        const widthOffset = (240 - mindMapWidth) / 2
        top = -ratio * y
        left = widthOffset - ratio * x
      }
      const style = divRef.current!.style
      style.width = `${screenDivWidth}px`
      style.height = `${screenDivHeight}px`
      style.top = `${top}px`
      style.left = `${left}px`
      // debugger
    }, 200)
    const divEl = divRef.current!
    const imgEl = imgRef.current!
    let isMouseDown = false
    const handleMouseDown = () => {
      mei.map.style.transition = 'none'
      isMouseDown = true
    }
    const handleMouseMove = (e: MouseEvent) => {
      if (!isMouseDown) return
      const dx = e.movementX
      const dy = e.movementY
      divEl.style.left = `${parseInt(divEl.style.left) + dx}px`
      divEl.style.top = `${parseInt(divEl.style.top) + dy}px`
      mei.move(-dx / ratioRef.current, -dy / ratioRef.current)
    }
    const handleEnd = () => {
      mei.map.style.transition = 'all 0.3s ease'
      isMouseDown = false
    } 

    updateSvg()
    updateMiniMap()
    const handleLinkDiv = () => {
      updateSvg()
      updateMiniMap()
    }
    const handleScroll = () => {
      if (isMouseDown) return
      updateMiniMap()
    }
    mei.bus.addListener('linkDiv', handleLinkDiv) // 好像节点操作必定会 linkDiv，那就不用监听operation了
    mei.bus.addListener('scale', updateMiniMap)
    mei.bus.addListener('move', handleScroll)

    const handleDivMove = (e: MouseEvent) => {
      isMouseDown = true
      const style = divEl.style
      let { offsetX, offsetY } = e
      offsetX = offsetX - parseInt(style.width) / 2
      offsetY = offsetY - parseInt(style.height) / 2
      const lastLeft = parseInt(style.left)
      const lastTop = parseInt(style.top)
      const dx = offsetX - lastLeft
      const dy = offsetY - lastTop
      style.left = offsetX + 'px'
      style.top = offsetY + 'px'
      mei.move(-dx / ratioRef.current, -dy / ratioRef.current)
    }
    const off = on([
      {dom:divEl, evt:'mousedown', func:handleMouseDown},
      {dom:divEl, evt:'mousemove', func:handleMouseMove},
      {dom:divEl, evt:'mouseup', func:handleEnd},
      {dom:divEl, evt:'mouseleave', func:handleEnd},
      // 处理点哪去哪的情况
      {dom:imgEl, evt:'mousedown', func:handleDivMove},
    ])

    return () => {
      off()
      // mei.bus.removeListener('linkDiv', handleLinkDiv)
      // mei.bus.removeListener('scale', updateMiniMap)
    }
  }, [useMiniMap, mei])
  return (
    useMiniMap && (
      <div
        className=" absolute border-input border-2 rounded-md  bottom-4 left-16 w-64 h-64 p-2  overflow-hidden select-none "
        style={{
          backgroundColor: mei.theme.cssVar['--bgcolor'],
        }}
        onContextMenu={e => e.preventDefault()}
      >
        <div className="relative h-full w-full">
          <img ref={imgRef} className={'h-full w-full object-contain ' + (imgUrl ? 'opacity-100' : 'opacity-0')} src={imgUrl} />
          <div ref={divRef} className="absolute bg-slate-600 opacity-30 hotzone"></div>
        </div>
      </div>
    )
  )
}

export default () => {
  const mei = useMindElixirStore(state => state.mei)
  return mei ? <MiniMap mei={mei} /> : null
}
