import MindElixir, { Theme } from 'mind-elixir'
import bw from '@/theme/bw'
import dopamine from '@/theme/dopamine'
import dracula from '@/theme/dracula'
import tokyoNight from '@/theme/tokyo-night'

const themeArr = [MindElixir.THEME, MindElixir.DARK_THEME, bw, dopamine, dracula, tokyoNight]

export const themeMap = themeArr.reduce((acc, cur) => {
  acc[cur.name] = cur
  return acc
}, {} as Record<string, Theme>)
