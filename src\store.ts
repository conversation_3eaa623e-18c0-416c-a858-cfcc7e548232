import { MindElixirInstance, NodeObj } from 'mind-elixir'
import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { MindElixirLocal } from './api/map'

type EditState = {
  mei?: MindElixirInstance
  file?: MindElixirLocal
  currentNode?: NodeObj
  currentNodes: NodeObj[]
  useMiniMap: boolean
  nodeMenuCollapsed: boolean
  searchOpen: boolean
  hasSeenShortcutsDialog: boolean
}

type EditActions = {
  setMei: (mei: MindElixirInstance | undefined) => void
  setFile: (file: MindElixirLocal | undefined) => void
  setCurrentNodes: (nodes: NodeObj[]) => void
  toggleUseMiniMap: () => void
  setNodeMenuCollapsed: (collapsed: boolean) => void
  toggleNodeMenuCollapsed: () => void
  toggleSearchOpen: () => void
  setSearchOpen: (open: boolean) => void
  setHasSeenShortcutsDialog: (seen: boolean) => void
}

type UserSettings = {
  language: string
  theme: string
  proxy: string
  proxyEnabled: boolean
  geminiKey: string
  openaiKey: string
  openaiModel: string
  geminiModel: string
  openaiCompatibleModel: string
  baseUrl: string
  aiProvider: 'gemini' | 'openai' | 'openai-compatible'
  aiMode: 'normal' | 'context-aware'
  setLanguage: (language: string) => void
  setTheme: (theme: string) => void
  setProxy: (proxy: string) => void
  setProxyEnabled: (proxyEnabled: boolean) => void
  setGeminiKey: (geminiKey: string) => void
  setOpenaiKey: (openaiKey: string) => void
  setOpenaiModel: (openaiModel: string) => void
  setGeminiModel: (geminiModel: string) => void
  setOpenaiCompatibleModel: (openaiCompatibleModel: string) => void
  setBaseUrl: (baseUrl: string) => void
  setAiProvider: (aiProvider: 'gemini' | 'openai' | 'openai-compatible') => void
  setAiMode: (aiMode: 'normal' | 'context-aware') => void
}

export const useMindElixirStore = create<EditState & EditActions>()(
  persist(
    set => ({
      mei: undefined,
      file: undefined,
      currentNode: undefined,
      currentNodes: [],
      useMiniMap: false,
      nodeMenuCollapsed: false,
      searchOpen: false,
      hasSeenShortcutsDialog: false,
      setMei: mei => set({ mei }),
      setFile: file => set({ file }),
      setCurrentNodes: nodes => {
        if (nodes.length === 1) set({ currentNodes: nodes, currentNode: nodes[0] })
        else set({ currentNodes: nodes, currentNode: undefined })
      },
      toggleUseMiniMap: () => set(state => ({ useMiniMap: !state.useMiniMap })),
      setNodeMenuCollapsed: collapsed => set({ nodeMenuCollapsed: collapsed }),
      toggleNodeMenuCollapsed: () => set(state => ({ nodeMenuCollapsed: !state.nodeMenuCollapsed })),
      toggleSearchOpen: () => set(state => ({ searchOpen: !state.searchOpen })),
      setSearchOpen: open => set({ searchOpen: open }),
      setHasSeenShortcutsDialog: seen => set({ hasSeenShortcutsDialog: seen }),
    }),
    {
      name: 'mind-elixir-editor-state',
      partialize: state => ({
        nodeMenuCollapsed: state.nodeMenuCollapsed,
        useMiniMap: state.useMiniMap,
        hasSeenShortcutsDialog: state.hasSeenShortcutsDialog
      }),
    }
  )
)

export const useUserSettings = create<UserSettings>()(
  persist(
    set => ({
      language: 'en',
      theme: 'system',
      proxy: '',
      proxyEnabled: false,
      geminiKey: '',
      openaiKey: '',
      openaiModel: 'gpt-3.5-turbo',
      geminiModel: 'gemini-2.5-pro-exp-03-25',
      openaiCompatibleModel: 'gpt-3.5-turbo',
      baseUrl: '',
      aiProvider: 'gemini',
      aiMode: 'normal',
      setLanguage: language => set({ language }),
      setTheme: theme => set({ theme }),
      setProxy: proxy => set({ proxy }),
      setProxyEnabled: proxyEnabled => set({ proxyEnabled }),
      setGeminiKey: geminiKey => set({ geminiKey }),
      setOpenaiKey: openaiKey => set({ openaiKey }),
      setOpenaiModel: openaiModel => set({ openaiModel }),
      setGeminiModel: geminiModel => set({ geminiModel }),
      setOpenaiCompatibleModel: openaiCompatibleModel => set({ openaiCompatibleModel }),
      setBaseUrl: baseUrl => set({ baseUrl }),
      setAiProvider: aiProvider => set({ aiProvider }),
      setAiMode: aiMode => set({ aiMode }),
    }),
    {
      name: 'user-settings',
    }
  )
)

export interface UserInfo {
  _id: string
  providerAccountId: string
  provider: string
  email: string
  image: string
  name: string
  uuid: string
  type: string
}

export const useUserInfoStore = create<{
  userInfo: UserInfo
  setUserInfo: (userInfo: UserInfo) => void
}>()(
  persist(
    set => ({
      userInfo: {
        _id: '',
        providerAccountId: '',
        provider: '',
        email: '',
        image: '',
        name: '',
        uuid: '',
        type: '',
      },
      setUserInfo: userInfo => set({ userInfo }),
    }),
    {
      name: 'user-info',
    }
  )
)

export const useSearchMapStore = create<{
  searchMapList: MindElixirLocal[]
  setSearchMap: (searchMapList: MindElixirLocal[]) => void
  appendSearchMap: (s: MindElixirLocal) => void
}>()(set => ({
  searchMapList: [],
  setSearchMap: searchMapList => set({ searchMapList }),
  appendSearchMap: searchMap => set(state => ({ searchMapList: [...state.searchMapList, searchMap] })),
}))
