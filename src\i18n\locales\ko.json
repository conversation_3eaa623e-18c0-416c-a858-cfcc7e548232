{"common": {"save": "저장", "cancel": "취소", "delete": "삭제", "edit": "편집", "create": "생성", "search": "검색", "searchMindMap": "마인드맵 검색...", "searchTags": "태그 검색...", "tags": "태그", "allTags": "모든 태그", "noTagsFound": "태그를 찾을 수 없습니다.", "noMapsWithTag": "이 태그가 포함된 마인드맵이 없습니다.", "clearFilter": "필터 지우기", "back": "뒤로", "system": "시스템", "email": "이메일", "username": "사용자명", "avatar": "아바타", "password": "비밀번호", "logout": "로그아웃", "dashboard": "대시보드", "recycleBin": "휴지통", "noData": "데이터 없음", "recycleBinEmpty": "휴지통이 비어있습니다.", "templatesEmpty": "사용 가능한 템플릿이 없습니다.", "emptyRecycleBin": "휴지통 비우기", "preview": "미리보기", "restore": "복원", "deletePermanently": "영구 삭제", "lastEdited": "마지막 편집", "upload": "업로드", "file": "파일", "link": "링크", "selectFile": "파일 선택", "enterLink": "링크 입력", "import": "가져오기", "download": "다운로드", "clipboard": "클립보드", "pasteJson": "JSON 붙여넣기", "dataCorrupted": "데이터 손상됨", "cannotPreview": "미리보기 불가", "error": "오류"}, "mindmap": {"newMap": "새로 만들기", "importMap": "가져오기", "importMapDescription": "JSON 파일 또는 URL에서 마인드맵 가져오기", "openMap": "마인드맵 열기", "addNode": "노드 추가", "deleteNode": "노드 삭제", "addChild": "자식 노드 추가", "addSibling": "형제 노드 추가", "centerMap": "맵 중앙 정렬", "zoomIn": "확대", "zoomOut": "축소", "mindMap": "마인드맵", "outline": "개요", "saveAsNewMap": "새 맵으로 저장", "mindMapUpdated": "마인드맵이 업데이트되었습니다", "mindMapUploaded": "마인드맵이 업로드되었습니다", "outlineMode": "개요 모드", "saved": "저장됨", "importFailed": "가져오기 실패", "savedLocally": "로컬에 저장됨", "newMindMap": "새 마인드맵"}, "nodeMenu": {"general": "일반", "note": "노트", "image": "이미지", "selectNode": "노드를 선택해주세요.", "globalSettings": "전역 설정", "mindMapSettings": "마인드맵 설정", "theme": "테마", "source": "소스", "fileInfo": "파일 정보", "createdAt": "생성일", "updatedAt": "수정일", "enterSource": "소스를 입력하세요 (예: 비디오 URL)"}, "dialog": {"confirmDeletion": "삭제 확인", "clearRecycleBinConfirmation": "휴지통을 비우시겠습니까? 이 작업은 되돌릴 수 없습니다.", "limitReached": "한도 도달", "subscriptionRequired": "12개 이상의 마인드맵을 생성했습니다. 무제한 액세스를 잠금 해제하고 더 많이 생성하려면 구독하세요.", "maybeLater": "나중에", "subscribeNow": "지금 구독", "unsavedChangesAlert": "저장되지 않은 변경사항이 있습니다. 정말 종료하시겠습니까?"}, "operation": {"addChild": "자식 노드 추가", "addParent": "부모 노드 추가", "addSibling": "형제 노드 추가", "remove": "제거", "focus": "포커스", "unfocus": "포커스 해제", "moveUp": "위로 이동", "moveDown": "아래로 이동", "link": "링크", "linkTips": "연결할 노드를 클릭하세요", "summary": "요약"}, "contextMenu": {"ai": "AI 생성", "aiWithPrompt": "프롬프트로 AI 생성", "generate": "생성!", "prompt": "프롬프트", "addChild": "자식 노드 추가", "addParent": "부모 노드 추가", "addSibling": "형제 노드 추가", "remove": "제거", "focus": "포커스", "unfocus": "포커스 해제", "moveUp": "위로 이동", "moveDown": "아래로 이동", "link": "링크", "summary": "요약"}, "menu": {"mindElixir": "MindElixir", "open": "열기", "export": "내보내기", "theme": "테마", "appearance": "외관", "system": "시스템", "light": "라이트", "dark": "다크", "shortcuts": "단축키", "exit": "종료"}, "shortcuts": {"action": "동작", "shortcut": "단축키", "displaySide": "사이드 표시", "displayLeft": "왼쪽 표시", "displayRight": "오른쪽 표시", "save": "저장", "undo": "실행 취소", "copy": "복사", "paste": "붙여넣기", "cut": "잘라내기", "moveToCenter": "중앙으로 이동", "editTopic": "주제 편집", "zoomIn": "확대", "zoomOut": "축소", "resetZoom": "줌 재설정", "toggleNodeMenu": "노드 메뉴 토글", "showShortcutsDialog": "단축키 대화상자 표시", "toggleOutlineMode": "아웃라인 모드 전환", "hint": "{{key}}를 눌러 이 대화상자를 다시 표시할 수 있습니다"}, "settings": {"title": "설정", "theme": "테마", "light": "라이트", "dark": "다크", "account": {"title": "계정", "changeEmail": "이메일 변경", "changePassword": "비밀번호 변경", "pleaseLogin": "로그인", "logout": "로그아웃", "userProfile": "사용자 프로필", "upgradeToPro": "프로로 업그레이드", "checkLicense": "라이선스 확인", "githubLogin": "<PERSON><PERSON><PERSON> 로그인", "googleLogin": "Google 로그인"}, "appearance": {"title": "외관", "darkMode": "다크 모드", "darkModeDescription": "라이트 모드와 다크 모드 전환", "theme": "테마", "selectTheme": "테마 선택", "light": "라이트", "dark": "다크", "system": "시스템"}, "proxy": {"title": "프록시", "enable": "프록시 활성화", "url": "프록시 URL"}, "ai": {"title": "AI 설정", "mode": "AI 모드", "geminiKey": "Gemini API 키", "openaiKey": "OpenAI API 키", "openaiModel": "OpenAI 모델", "baseUrl": "기본 URL", "aiProvider": "AI 제공업체", "normal": "일반", "contextAware": "컨텍스트 인식 확장", "openaiCompatible": "OpenAI 호환", "selectModel": "모델 선택", "loading": "모델 로딩 중...", "errorFetchingModels": "모델 가져오기 오류", "searchModel": "모델 검색...", "noModelFound": "모델을 찾을 수 없습니다.", "geminiModel": "Gemini 모델", "checkSettings": "AI 설정을 확인하세요", "gemini": "Gemini", "openai": "OpenAI", "manualInputRequired": "모델 목록을 가져올 수 없습니다. 수동으로 입력하세요"}, "notifications": {"title": "알림", "emailNotifications": "이메일 알림", "emailDescription": "활동에 대한 이메일 받기", "pushNotifications": "푸시 알림", "pushDescription": "새로운 기능 알림 받기"}, "language": {"title": "언어", "preferred": "선호 언어", "selectLanguage": "언어 선택", "select": "언어 선택"}, "privacy": {"title": "개인정보", "publicProfile": "공개 프로필", "publicProfileDescription": "다른 사람이 당신의 마인드맵을 볼 수 있도록 하기", "shareAnalytics": "분석 데이터 공유", "shareAnalyticsDescription": "제품 개선에 도움을 주세요"}, "storage": {"title": "데이터 저장소", "openDataFolder": "데이터 폴더 열기", "openDataFolderDescription": "마인드맵이 저장된 폴더 열기"}, "about": {"title": "정보", "version": "버전"}}, "auth": {"loginSuccess": "로그인 성공", "logoutSuccess": "로그아웃 성공"}, "error": {"general": "오류가 발생했습니다", "importFailed": "가져오기 실패, 형식 오류일 수 있습니다", "unauthorized": "로그인이 만료되었습니다. 다시 로그인해주세요"}, "import": {"jsonFileDescription": "Mind Elixir에서 내보낸 JSON 파일 가져오기", "fileDescriptionBatch": "JSON 및 XMind 파일 일괄 가져오기 지원", "linkDescription": "Mind Elixir Cloud에서 가져오기", "clipboardDescription": "JSON 콘텐츠 붙여넣기", "batchTags": "일괄 태그", "batchTagsPlaceholder": "태그 입력, 쉼표로 구분", "batchTagsDescription": "모든 가져온 파일에 동일한 태그를 추가하여 태그 페이지에서 쉽게 분류할 수 있습니다", "batchImportSuccess": "{{count}}개 파일을 성공적으로 가져왔습니다", "batchImportPartialSuccess": "{{success}}개 파일을 성공적으로 가져왔고, {{fail}}개 파일 가져오기에 실패했습니다", "batchImportFailed": "일괄 가져오기 실패, 모든 파일을 가져올 수 없습니다"}, "search": {"placeholder": "마인드맵에서 검색...", "noResults": "결과를 찾을 수 없습니다", "results": "{{current}}/{{total}} 개 결과"}, "tips": {"pressF1ToCenter": "F1을 눌러 마인드맵 중심으로 돌아가기"}}