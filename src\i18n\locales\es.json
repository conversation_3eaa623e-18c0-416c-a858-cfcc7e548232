{"common": {"save": "Guardar", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Eliminar", "edit": "<PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "search": "Buscar", "searchMindMap": "Buscar en mapa mental...", "searchTags": "Buscar etiquetas...", "tags": "Etiquetas", "allTags": "Todas las etiquetas", "noTagsFound": "No se encontraron etiquetas.", "noMapsWithTag": "No hay mapas mentales con esta etiqueta.", "clearFilter": "Limpiar filtro", "back": "Atrás", "system": "Sistema", "email": "Correo electrónico", "username": "Nombre de usuario", "avatar": "Avatar", "password": "Contraseña", "logout": "<PERSON><PERSON><PERSON>", "dashboard": "Panel de control", "recycleBin": "Papelera de reciclaje", "noData": "Sin datos", "recycleBinEmpty": "La papelera de reciclaje está vacía.", "templatesEmpty": "No hay plantillas disponibles.", "emptyRecycleBin": "Vaciar pap<PERSON>ra de reciclaje", "preview": "Vista previa", "restore": "Restaurar", "deletePermanently": "Eliminar permanentemente", "lastEdited": "Última edición", "upload": "Subir", "file": "Archivo", "link": "Enlace", "selectFile": "Seleccionar archivo", "enterLink": "Introducir enlace", "import": "Importar", "download": "<PERSON><PERSON><PERSON>", "clipboard": "Portapapeles", "pasteJson": "<PERSON><PERSON><PERSON>", "dataCorrupted": "<PERSON><PERSON>", "cannotPreview": "No se puede previsualizar", "error": "Error"}, "mindmap": {"newMap": "<PERSON><PERSON><PERSON> nuevo", "importMap": "Importar", "importMapDescription": "Importar mapa mental desde archivo JSON o URL", "openMap": "<PERSON><PERSON><PERSON> mapa mental", "addNode": "<PERSON><PERSON><PERSON>", "deleteNode": "Eliminar nodo", "addChild": "<PERSON><PERSON><PERSON>o hijo", "addSibling": "<PERSON><PERSON><PERSON>o hermano", "centerMap": "Centrar mapa", "zoomIn": "Acercar", "zoomOut": "<PERSON><PERSON><PERSON>", "mindMap": "Mapa mental", "outline": "Esquema", "saveAsNewMap": "Guardar como nuevo mapa", "mindMapUpdated": "Mapa mental actualizado", "mindMapUploaded": "Mapa mental subido", "outlineMode": "<PERSON><PERSON> es<PERSON>", "saved": "Guardado", "importFailed": "Error al importar", "savedLocally": "Guardado localmente", "newMindMap": "Nuevo Mapa Mental"}, "nodeMenu": {"general": "General", "note": "<PERSON>a", "image": "Imagen", "selectNode": "Por favor, seleccione un nodo.", "globalSettings": "Configuración Global", "mindMapSettings": "Configuración del Mapa Mental", "theme": "<PERSON><PERSON>", "source": "Fuente", "fileInfo": "Información del Archivo", "createdAt": "Creado en", "updatedAt": "Actualizado en", "enterSource": "Ingrese la fuente (ej. URL del video)"}, "dialog": {"confirmDeletion": "Confirmar eliminación", "clearRecycleBinConfirmation": "¿Está seguro de que desea vaciar la papelera de reciclaje? Esta acción no se puede deshacer.", "limitReached": "Lí<PERSON>", "subscriptionRequired": "Ha creado más de 12 mapas mentales. Por favor, suscríbase para desbloquear acceso ilimitado y crear más.", "maybeLater": "Tal vez más tarde", "subscribeNow": "Suscribirse ahora", "unsavedChangesAlert": "Tiene cambios sin guardar. ¿Está seguro de que desea salir?"}, "operation": {"addChild": "<PERSON><PERSON><PERSON>o hijo", "addParent": "<PERSON><PERSON><PERSON>o padre", "addSibling": "<PERSON><PERSON><PERSON>o hermano", "remove": "Eliminar", "focus": "Enfocar", "unfocus": "Desenfocar", "moveUp": "Mover a<PERSON>ba", "moveDown": "Mover abajo", "link": "<PERSON><PERSON><PERSON>", "linkTips": "Por favor, haga clic en el nodo a enlazar", "summary": "Resumen"}, "contextMenu": {"ai": "Creación con IA", "aiWithPrompt": "Creación con IA y prompt", "generate": "¡Generar!", "prompt": "Prompt", "addChild": "<PERSON><PERSON><PERSON>o hijo", "addParent": "<PERSON><PERSON><PERSON>o padre", "addSibling": "<PERSON><PERSON><PERSON>o hermano", "remove": "Eliminar", "focus": "Enfocar", "unfocus": "Desenfocar", "moveUp": "Mover a<PERSON>ba", "moveDown": "Mover abajo", "link": "<PERSON><PERSON><PERSON>", "summary": "Resumen"}, "menu": {"mindElixir": "MindElixir", "open": "Abrir", "export": "Exportar", "theme": "<PERSON><PERSON>", "appearance": "Apariencia", "system": "Sistema", "light": "<PERSON><PERSON><PERSON>", "dark": "Oscuro", "shortcuts": "Atajos de teclado", "exit": "Salir"}, "shortcuts": {"action": "Acción", "shortcut": "Atajo de teclado", "displaySide": "Mostrar lateral", "displayLeft": "Mostrar izquierda", "displayRight": "<PERSON><PERSON> derecha", "save": "Guardar", "undo": "<PERSON><PERSON><PERSON>", "copy": "Copiar", "paste": "<PERSON><PERSON><PERSON>", "cut": "Cortar", "moveToCenter": "Mover al centro", "editTopic": "<PERSON><PERSON> tema", "zoomIn": "Acercar", "zoomOut": "<PERSON><PERSON><PERSON>", "resetZoom": "Restablecer zoom", "toggleNodeMenu": "Alternar menú de nodo", "showShortcutsDialog": "Mostrar diálogo de atajos", "toggleOutlineMode": "Alternar modo esquema", "hint": "Presione {{key}} para mostrar este diálogo nuevamente"}, "settings": {"title": "Configuración", "theme": "<PERSON><PERSON>", "light": "<PERSON><PERSON><PERSON>", "dark": "Oscuro", "account": {"title": "C<PERSON><PERSON>", "changeEmail": "Cambiar correo electrónico", "changePassword": "Cambiar contraseña", "pleaseLogin": "In<PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON>", "userProfile": "Perfil de usuario", "upgradeToPro": "Actualizar a Pro", "checkLicense": "Verificar licencia", "githubLogin": "Iniciar <PERSON><PERSON><PERSON> con <PERSON>", "googleLogin": "Iniciar se<PERSON><PERSON> con Google"}, "appearance": {"title": "Apariencia", "darkMode": "<PERSON><PERSON> oscuro", "darkModeDescription": "Cambiar entre modo claro y oscuro", "theme": "<PERSON><PERSON>", "selectTheme": "<PERSON><PERSON><PERSON><PERSON><PERSON> tema", "light": "<PERSON><PERSON><PERSON>", "dark": "Oscuro", "system": "Sistema"}, "proxy": {"title": "Proxy", "enable": "Habilitar proxy", "url": "URL del proxy"}, "ai": {"title": "Configuración de IA", "mode": "Modo de IA", "geminiKey": "Clave API de Gemini", "openaiKey": "Clave API de OpenAI", "openaiModel": "Modelo de OpenAI", "baseUrl": "URL base", "aiProvider": "Proveedor de IA", "normal": "Normal", "contextAware": "Expansión consciente del contexto", "openaiCompatible": "Compatible con OpenAI", "selectModel": "Seleccionar modelo", "loading": "Cargando modelos...", "errorFetchingModels": "Error al obtener modelos", "searchModel": "Buscar modelo...", "noModelFound": "No se encontró ningún modelo.", "geminiModel": "<PERSON><PERSON> de Gemini", "checkSettings": "Por favor, verifique la configuración de IA", "gemini": "Gemini", "openai": "OpenAI", "manualInputRequired": "No se puede obtener la lista de modelos, por favor ingrese manualmente"}, "notifications": {"title": "Notificaciones", "emailNotifications": "Notificaciones por correo electrónico", "emailDescription": "Recibir correos electrónicos sobre su actividad", "pushNotifications": "Notificaciones push", "pushDescription": "Recibir notificaciones sobre nuevas funciones"}, "language": {"title": "Idioma", "preferred": "Idioma preferido", "selectLanguage": "Seleccionar idioma", "select": "Seleccionar idioma"}, "privacy": {"title": "Privacidad", "publicProfile": "<PERSON><PERSON><PERSON>", "publicProfileDescription": "Permit<PERSON> que otros vean sus mapas mentales", "shareAnalytics": "Compartir datos analíticos", "shareAnalyticsDescription": "Ayúdanos a mejorar nuestro producto"}, "storage": {"title": "Almacenamiento de datos", "openDataFolder": "<PERSON><PERSON><PERSON> de da<PERSON>", "openDataFolderDescription": "<PERSON><PERSON>r la carpeta donde se almacenan sus mapas mentales"}, "about": {"title": "Acerca de", "version": "Versión"}}, "auth": {"loginSuccess": "Inicio de sesión exitoso", "logoutSuccess": "Cierre de sesión exitoso"}, "error": {"general": "Ha ocurrido un error", "importFailed": "Error al importar, posiblemente debido a un error de formato", "unauthorized": "Sesión expirada, por favor inicie sesión nuevamente"}, "import": {"jsonFileDescription": "Importar archivo JSON exportado desde Mind Elixir", "fileDescriptionBatch": "Soporta importación por lotes de archivos JSON y XMind", "linkDescription": "Importar desde Mind Elixir Cloud", "clipboardDescription": "Pegar contenido JSON", "batchTags": "Etiquetas por lotes", "batchTagsPlaceholder": "Ingrese etiquetas, separadas por comas", "batchTagsDescription": "Agregar las mismas etiquetas a todos los archivos importados para facilitar la categorización en la vista de etiquetas", "batchImportSuccess": "{{count}} archivos importados exitosamente", "batchImportPartialSuccess": "{{success}} archivos importados exitosamente, {{fail}} archivos fallaron al importar", "batchImportFailed": "Importación por lotes falló, todos los archivos no pudieron ser importados"}, "search": {"placeholder": "Buscar en mapa mental...", "noResults": "No se encontraron resultados", "results": "{{current}}/{{total}} resultados"}, "tips": {"pressF1ToCenter": "Presiona F1 para volver al centro del mapa mental"}}