import { ArrowLeft, Search, Tag } from 'lucide-react'
import { But<PERSON> } from '../ui/button'
import { Input } from '../ui/input'
import { useCallback, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { listMaps, MindElixirLocal, searchMaps } from '@/api/map'
import PreviewCard from '../mind-elixir/PreviewCard'
import { ScrollArea } from '../ui/scroll-area'
import { useSearchMapStore } from '@/store'
import { Badge } from '../ui/badge'
import { NodeObj } from 'mind-elixir'

// Cache for all tags
let cachedTags: string[] | null = null

const getAllTags = async (): Promise<string[]> => {
  // Return cached tags if available
  if (cachedTags !== null) {
    return cachedTags
  }

  const { list } = await listMaps(1, 999)
  const tagSet = new Set<string>()

  const extractTags = (node: NodeObj) => {
    if (node.tags && Array.isArray(node.tags)) {
      node.tags.forEach((tag: string) => tagSet.add(tag))
    }
    if (node.children && Array.isArray(node.children)) {
      node.children.forEach(extractTags)
    }
  }

  list.forEach(map => {
    if ('content' in map && map.content.nodeData) {
      extractTags(map.content.nodeData)
    }
  })

  // Store in cache
  cachedTags = Array.from(tagSet).sort()
  return cachedTags
}

// Force refresh of tag cache (to be called when maps are updated)
export const invalidateTagCache = () => {
  cachedTags = null
}

const TagList = () => {
  const { t } = useTranslation()
  const [isLoading, setIsLoading] = useState(true)
  const [allTags, setAllTags] = useState<string[]>([])
  const [filteredTags, setFilteredTags] = useState<string[]>([])
  const [selectedTag, setSelectedTag] = useState<string | null>(null)
  const { searchMapList, setSearchMap } = useSearchMapStore()
  const [keyword, setKeyword] = useState('')

  // 检测移动端
  const [isMobile, setIsMobile] = useState(false)
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768) // md断点
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)

    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  const loadTags = useCallback(async () => {
    setIsLoading(true)
    const tags = await getAllTags()
    setAllTags(tags)
    setFilteredTags(tags)
    setIsLoading(false)
  }, [])

  useEffect(() => {
    loadTags()
  }, [loadTags])

  useEffect(() => {
    return () => {
      invalidateTagCache()
    }
  }, [])

  const handleTagClick = async (tag: string) => {
    setIsLoading(true)
    setSelectedTag(tag)
    await searchMaps('maps', 'tag', tag)
    setIsLoading(false)
  }

  const handleSearch = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const searchValue = e.target.value
      setKeyword(searchValue)

      if (searchValue === '') {
        // Reset to all tags when search is cleared
        setFilteredTags(allTags)
        setSearchMap([])
        return
      }

      // Filter tags based on search term
      const filtered = allTags.filter(tag => tag.toLowerCase().includes(searchValue.toLowerCase()))

      setFilteredTags(filtered)
    },
    [allTags, setSearchMap]
  )

  const clearTagSelection = useCallback(() => {
    setSelectedTag(null)
    setSearchMap([])
    setFilteredTags(allTags) // Restore all tags
    setKeyword('')
  }, [allTags, setSearchMap])

  // 刷新标签缓存和当前标签的思维导图列表
  const refreshTagsAndMaps = useCallback(async () => { 
    if (selectedTag) {
      await searchMaps('maps', 'tag', selectedTag)
    }
  }, [selectedTag])

  return (
    <div>
      <div className="p-3 flex gap-2">
        {selectedTag ? (
          <Button size="sm" className="h-8" onClick={clearTagSelection}>
            <ArrowLeft className="h-4 w-4 mr-1" />
            {t('common.back')}
          </Button>
        ) : (
          <div className="relative flex-1">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input type="search" placeholder={t('common.searchTags')} value={keyword} className="pl-8 h-8" onChange={handleSearch} />
          </div>
        )}
      </div>

      <ScrollArea
        style={{
          height: isMobile ? 'calc(100vh - 160px)' : 'calc(100vh - 56px)',
        }}
      >
        {!isLoading && (
          <div className="flex-1 overflow-auto p-3">
            {selectedTag ? (
              <div>
                <div className="mb-4 flex items-center">
                  <Tag className="h-4 w-4 mr-2" />
                  <span className="font-medium">{selectedTag}</span>
                </div>

                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
                  {searchMapList.length > 0 ? (
                    searchMapList.map((map: MindElixirLocal, i: number) => <PreviewCard key={i} map={map} onDone={refreshTagsAndMaps} type="map" />)
                  ) : (
                    <div className="col-span-full text-center py-8 text-muted-foreground">{t('common.noMapsWithTag')}</div>
                  )}
                </div>
              </div>
            ) : (
              <div>
                <div className="mb-4 flex items-center">
                  <Tag className="h-4 w-4 mr-2" />
                  <span className="font-medium">{t('common.allTags')}</span>
                </div>

                <div className="flex flex-wrap gap-2">
                  {filteredTags.length > 0 ? (
                    filteredTags.map((tag, index) => (
                      <Badge key={index} className="cursor-pointer hover:bg-primary" onClick={() => handleTagClick(tag)}>
                        {tag}
                      </Badge>
                    ))
                  ) : (
                    <>
                      <div className="w-full text-center py-8 text-muted-foreground">{t('common.noTagsFound')}</div>
                    </>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </ScrollArea>
    </div>
  )
}

export default TagList
