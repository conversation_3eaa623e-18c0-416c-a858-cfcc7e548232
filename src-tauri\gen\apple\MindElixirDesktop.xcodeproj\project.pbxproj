// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		2E33ED86D2471B164DC2C0CB /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 7C812208BB548F27E5BDD69E /* LaunchScreen.storyboard */; };
		2E433B32B025A813E9A27980 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 51C71585B4ACA7884F807168 /* QuartzCore.framework */; };
		3C749197D642D5E7F46CE0A3 /* libapp.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 7BE1E54D10678388E6064C8C /* libapp.a */; };
		4E0746871333A376F6ADFFC4 /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B45C159398E892D99CE4A79D /* Security.framework */; };
		4E2D71714B45774CAAA4B494 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 06F3E1DBC9D05394DEB15231 /* CoreGraphics.framework */; };
		773099D9F9961275109C2CB4 /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0D079691EF85F9F709D88239 /* Metal.framework */; };
		82CF26C229C1B3B620DBA0F9 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1C89686C131A8006E502C123 /* UIKit.framework */; };
		860C2A3D292CCD75245D8DC7 /* MetalKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B58EC01F182B506F73A539E7 /* MetalKit.framework */; };
		9862B4AE03047A8B2BC9957F /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 0FEB101C79457946479E0131 /* Assets.xcassets */; };
		CF0612AC1DC42D66FCCB5BC5 /* main.mm in Sources */ = {isa = PBXBuildFile; fileRef = 657B0D5345488B27E5CEAB15 /* main.mm */; };
		E45E10FA514E072D89E7585B /* WebKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 5D7D5C3643278CE69572FE2C /* WebKit.framework */; };
		F42BD97E71AB17CC88E5FCE7 /* assets in Resources */ = {isa = PBXBuildFile; fileRef = 4907433119F8F914176ED252 /* assets */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		06F3E1DBC9D05394DEB15231 /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		0D079691EF85F9F709D88239 /* Metal.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Metal.framework; path = System/Library/Frameworks/Metal.framework; sourceTree = SDKROOT; };
		0FEB101C79457946479E0131 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		1C89686C131A8006E502C123 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		25E9DB2DB1FAB94066C08D6E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist; path = Info.plist; sourceTree = "<group>"; };
		35CE1F358D0CF2D5BEE21CBC /* mod.rs */ = {isa = PBXFileReference; path = mod.rs; sourceTree = "<group>"; };
		4907433119F8F914176ED252 /* assets */ = {isa = PBXFileReference; lastKnownFileType = folder; path = assets; sourceTree = SOURCE_ROOT; };
		4FEDE82E963A810077D8823D /* mindmap.rs */ = {isa = PBXFileReference; path = mindmap.rs; sourceTree = "<group>"; };
		51C71585B4ACA7884F807168 /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		5D7D5C3643278CE69572FE2C /* WebKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WebKit.framework; path = System/Library/Frameworks/WebKit.framework; sourceTree = SDKROOT; };
		6175A6BCA48E05AD653C1530 /* axum.rs */ = {isa = PBXFileReference; path = axum.rs; sourceTree = "<group>"; };
		657B0D5345488B27E5CEAB15 /* main.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = main.mm; sourceTree = "<group>"; };
		6E2A83C335C7E29AF7E29566 /* calculator.rs */ = {isa = PBXFileReference; path = calculator.rs; sourceTree = "<group>"; };
		7BE1E54D10678388E6064C8C /* libapp.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libapp.a; sourceTree = "<group>"; };
		7C812208BB548F27E5BDD69E /* LaunchScreen.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = LaunchScreen.storyboard; sourceTree = "<group>"; };
		8C040115748770EC3BD486B8 /* main.rs */ = {isa = PBXFileReference; path = main.rs; sourceTree = "<group>"; };
		9196C6A4168096F31E1574D6 /* bindings.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = bindings.h; sourceTree = "<group>"; };
		AF74D1D893B656B6B6322A15 /* mod.rs */ = {isa = PBXFileReference; path = mod.rs; sourceTree = "<group>"; };
		AFDB0543BB3BD0947978C7D8 /* axum_router.rs */ = {isa = PBXFileReference; path = axum_router.rs; sourceTree = "<group>"; };
		B45C159398E892D99CE4A79D /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		B58EC01F182B506F73A539E7 /* MetalKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MetalKit.framework; path = System/Library/Frameworks/MetalKit.framework; sourceTree = SDKROOT; };
		C654885658040558BBFF57FA /* MindElixirDesktop_iOS.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = MindElixirDesktop_iOS.entitlements; sourceTree = "<group>"; };
		D26C7FDE9D6B684DE9B45246 /* MindElixirDesktop_iOS.app */ = {isa = PBXFileReference; includeInIndex = 0; lastKnownFileType = wrapper.application; path = MindElixirDesktop_iOS.app; sourceTree = BUILT_PRODUCTS_DIR; };
		D80BE7766F6D189617E4B9E4 /* std_io.rs */ = {isa = PBXFileReference; path = std_io.rs; sourceTree = "<group>"; };
		E4881A0DC04236FFC6939D1F /* lib.rs */ = {isa = PBXFileReference; path = lib.rs; sourceTree = "<group>"; };
		E75D84DC2B9275BD6B659971 /* encrypt.rs */ = {isa = PBXFileReference; path = encrypt.rs; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		5F736AC1F40426329592F846 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3C749197D642D5E7F46CE0A3 /* libapp.a in Frameworks */,
				4E2D71714B45774CAAA4B494 /* CoreGraphics.framework in Frameworks */,
				773099D9F9961275109C2CB4 /* Metal.framework in Frameworks */,
				860C2A3D292CCD75245D8DC7 /* MetalKit.framework in Frameworks */,
				2E433B32B025A813E9A27980 /* QuartzCore.framework in Frameworks */,
				4E0746871333A376F6ADFFC4 /* Security.framework in Frameworks */,
				82CF26C229C1B3B620DBA0F9 /* UIKit.framework in Frameworks */,
				E45E10FA514E072D89E7585B /* WebKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		27459312F5FD5FFB9F90FC00 /* Externals */ = {
			isa = PBXGroup;
			children = (
			);
			path = Externals;
			sourceTree = "<group>";
		};
		33EC4449AE664DF913214E3F /* mcp */ = {
			isa = PBXGroup;
			children = (
				AFDB0543BB3BD0947978C7D8 /* axum_router.rs */,
				6175A6BCA48E05AD653C1530 /* axum.rs */,
				35CE1F358D0CF2D5BEE21CBC /* mod.rs */,
				D80BE7766F6D189617E4B9E4 /* std_io.rs */,
				4E47A059F556FF2A0F3D1CD6 /* common */,
			);
			path = mcp;
			sourceTree = "<group>";
		};
		40DEA72DD6A10623B3597155 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				06F3E1DBC9D05394DEB15231 /* CoreGraphics.framework */,
				7BE1E54D10678388E6064C8C /* libapp.a */,
				0D079691EF85F9F709D88239 /* Metal.framework */,
				B58EC01F182B506F73A539E7 /* MetalKit.framework */,
				51C71585B4ACA7884F807168 /* QuartzCore.framework */,
				B45C159398E892D99CE4A79D /* Security.framework */,
				1C89686C131A8006E502C123 /* UIKit.framework */,
				5D7D5C3643278CE69572FE2C /* WebKit.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		4383C0D69E77586D535F6161 /* src */ = {
			isa = PBXGroup;
			children = (
				E75D84DC2B9275BD6B659971 /* encrypt.rs */,
				E4881A0DC04236FFC6939D1F /* lib.rs */,
				8C040115748770EC3BD486B8 /* main.rs */,
				33EC4449AE664DF913214E3F /* mcp */,
			);
			name = src;
			path = ../../src;
			sourceTree = "<group>";
		};
		4E47A059F556FF2A0F3D1CD6 /* common */ = {
			isa = PBXGroup;
			children = (
				6E2A83C335C7E29AF7E29566 /* calculator.rs */,
				4FEDE82E963A810077D8823D /* mindmap.rs */,
				AF74D1D893B656B6B6322A15 /* mod.rs */,
			);
			path = common;
			sourceTree = "<group>";
		};
		574F8FBC66817D1F4E02B7F8 = {
			isa = PBXGroup;
			children = (
				4907433119F8F914176ED252 /* assets */,
				0FEB101C79457946479E0131 /* Assets.xcassets */,
				7C812208BB548F27E5BDD69E /* LaunchScreen.storyboard */,
				27459312F5FD5FFB9F90FC00 /* Externals */,
				E778243FA1845D17FFA7FEEB /* MindElixirDesktop_iOS */,
				CA61BA8F16E46B97537A0819 /* Sources */,
				4383C0D69E77586D535F6161 /* src */,
				40DEA72DD6A10623B3597155 /* Frameworks */,
				88ADA7AC9299F3CBDD05C886 /* Products */,
			);
			sourceTree = "<group>";
		};
		58CFE1922F3CA15570021B80 /* MindElixirDesktop */ = {
			isa = PBXGroup;
			children = (
				657B0D5345488B27E5CEAB15 /* main.mm */,
				8789470D22965D3B5FA00854 /* bindings */,
			);
			path = MindElixirDesktop;
			sourceTree = "<group>";
		};
		8789470D22965D3B5FA00854 /* bindings */ = {
			isa = PBXGroup;
			children = (
				9196C6A4168096F31E1574D6 /* bindings.h */,
			);
			path = bindings;
			sourceTree = "<group>";
		};
		88ADA7AC9299F3CBDD05C886 /* Products */ = {
			isa = PBXGroup;
			children = (
				D26C7FDE9D6B684DE9B45246 /* MindElixirDesktop_iOS.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		CA61BA8F16E46B97537A0819 /* Sources */ = {
			isa = PBXGroup;
			children = (
				58CFE1922F3CA15570021B80 /* MindElixirDesktop */,
			);
			path = Sources;
			sourceTree = "<group>";
		};
		E778243FA1845D17FFA7FEEB /* MindElixirDesktop_iOS */ = {
			isa = PBXGroup;
			children = (
				25E9DB2DB1FAB94066C08D6E /* Info.plist */,
				C654885658040558BBFF57FA /* MindElixirDesktop_iOS.entitlements */,
			);
			path = MindElixirDesktop_iOS;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		F84F23A68FB9387C66A4FAC1 /* MindElixirDesktop_iOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8E9055BF41B5B2BC461D5916 /* Build configuration list for PBXNativeTarget "MindElixirDesktop_iOS" */;
			buildPhases = (
				1A694382049EA26B1DB7CA2D /* Build Rust Code */,
				E887A38F8DA0A2137AE43705 /* Sources */,
				3069254C6AF266E120BFDB5E /* Resources */,
				5F736AC1F40426329592F846 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = MindElixirDesktop_iOS;
			packageProductDependencies = (
			);
			productName = MindElixirDesktop_iOS;
			productReference = D26C7FDE9D6B684DE9B45246 /* MindElixirDesktop_iOS.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		52958C2822DB460319FAC0EA /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1430;
				TargetAttributes = {
				};
			};
			buildConfigurationList = 57A9A31809C3350F38DD677E /* Build configuration list for PBXProject "MindElixirDesktop" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = 574F8FBC66817D1F4E02B7F8;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 54;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				F84F23A68FB9387C66A4FAC1 /* MindElixirDesktop_iOS */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		3069254C6AF266E120BFDB5E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9862B4AE03047A8B2BC9957F /* Assets.xcassets in Resources */,
				2E33ED86D2471B164DC2C0CB /* LaunchScreen.storyboard in Resources */,
				F42BD97E71AB17CC88E5FCE7 /* assets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		1A694382049EA26B1DB7CA2D /* Build Rust Code */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Build Rust Code";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(SRCROOT)/Externals/x86_64/${CONFIGURATION}/libapp.a",
				"$(SRCROOT)/Externals/arm64/${CONFIGURATION}/libapp.a",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "pnpm tauri ios xcode-script -v --platform ${PLATFORM_DISPLAY_NAME:?} --sdk-root ${SDKROOT:?} --framework-search-paths \"${FRAMEWORK_SEARCH_PATHS:?}\" --header-search-paths \"${HEADER_SEARCH_PATHS:?}\" --gcc-preprocessor-definitions \"${GCC_PREPROCESSOR_DEFINITIONS:-}\" --configuration ${CONFIGURATION:?} ${FORCE_COLOR} ${ARCHS:?}";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		E887A38F8DA0A2137AE43705 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CF0612AC1DC42D66FCCB5BC5 /* main.mm in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		1F9AC7CBB15E81C2A94F7252 /* release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
			};
			name = release;
		};
		276372F8B41F5E66CBA059D5 /* debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"DEBUG=1",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
			};
			name = debug;
		};
		28D260D47F8394FD7D2D4AC6 /* debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ARCHS = (
					arm64,
				);
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_ENTITLEMENTS = MindElixirDesktop_iOS/MindElixirDesktop_iOS.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				ENABLE_BITCODE = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = x86_64;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\".\"",
				);
				INFOPLIST_FILE = MindElixirDesktop_iOS/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				"LIBRARY_SEARCH_PATHS[arch=arm64]" = "$(inherited) $(PROJECT_DIR)/Externals/arm64/$(CONFIGURATION) $(SDKROOT)/usr/lib/swift $(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME) $(TOOLCHAIN_DIR)/usr/lib/swift-5.0/$(PLATFORM_NAME)";
				"LIBRARY_SEARCH_PATHS[arch=x86_64]" = "$(inherited) $(PROJECT_DIR)/Externals/x86_64/$(CONFIGURATION) $(SDKROOT)/usr/lib/swift $(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME) $(TOOLCHAIN_DIR)/usr/lib/swift-5.0/$(PLATFORM_NAME)";
				PRODUCT_BUNDLE_IDENTIFIER = com.timerecord.mindelixir;
				PRODUCT_NAME = "Mind Elixir";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALID_ARCHS = arm64;
			};
			name = debug;
		};
		9E6727522B3B008714006F55 /* release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ARCHS = (
					arm64,
				);
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_ENTITLEMENTS = MindElixirDesktop_iOS/MindElixirDesktop_iOS.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				ENABLE_BITCODE = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = x86_64;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\".\"",
				);
				INFOPLIST_FILE = MindElixirDesktop_iOS/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				"LIBRARY_SEARCH_PATHS[arch=arm64]" = "$(inherited) $(PROJECT_DIR)/Externals/arm64/$(CONFIGURATION) $(SDKROOT)/usr/lib/swift $(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME) $(TOOLCHAIN_DIR)/usr/lib/swift-5.0/$(PLATFORM_NAME)";
				"LIBRARY_SEARCH_PATHS[arch=x86_64]" = "$(inherited) $(PROJECT_DIR)/Externals/x86_64/$(CONFIGURATION) $(SDKROOT)/usr/lib/swift $(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME) $(TOOLCHAIN_DIR)/usr/lib/swift-5.0/$(PLATFORM_NAME)";
				PRODUCT_BUNDLE_IDENTIFIER = com.timerecord.mindelixir;
				PRODUCT_NAME = "Mind Elixir";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALID_ARCHS = arm64;
			};
			name = release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		57A9A31809C3350F38DD677E /* Build configuration list for PBXProject "MindElixirDesktop" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				276372F8B41F5E66CBA059D5 /* debug */,
				1F9AC7CBB15E81C2A94F7252 /* release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = debug;
		};
		8E9055BF41B5B2BC461D5916 /* Build configuration list for PBXNativeTarget "MindElixirDesktop_iOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				28D260D47F8394FD7D2D4AC6 /* debug */,
				9E6727522B3B008714006F55 /* release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = debug;
		};
/* End XCConfigurationList section */
	};
	rootObject = 52958C2822DB460319FAC0EA /* Project object */;
}
