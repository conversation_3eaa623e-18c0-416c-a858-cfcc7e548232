[package]
name = "MindElixirDesktop"
version = "1.0.2"
description = "Lightweight app, heavyweight thinking. Mind Elixir expands your creativity in just 10MB, keeps all data securely local, and enhances your thinking with AI while privacy remains in your hands."
authors = ["Time Record"]
license = ""
repository = ""
edition = "2021"
rust-version = "1.77.2"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
name = "app_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2.0.2", features = [] }

[dependencies]
serde_json = "1.0"
serde = { version = "1.0", features = ["derive"] }
log = "0.4"
tauri = { version = "2.1.0", features = [] }
tauri-plugin-log = "2.0.0-rc"
tauri-plugin-http = { version = "2", features = ["unsafe-headers", "tracing", "cookies"] }
tokio = { version = "1.44", features = ["full", "process"] }
anyhow = "1.0.97"
rmcp = { git = "https://github.com/modelcontextprotocol/rust-sdk", branch = "main", features = ["server", "transport-sse-server", "transport-io"]  }
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
tracing = "0.1.41"
axum = { version = "0.8", features = ["macros"] }
tokio-util = { version = "0.7", features = ["codec"] }
tauri-plugin-opener = "2.2.6"
tauri-plugin-fs = "2"
tauri-plugin-dialog = "2"
aes-gcm = "0.10.3"
base64 = "0.22.1"
chrono = "0.4.41"
tauri-plugin-os = "2"
tauri-plugin-deep-link = "2"

[target."cfg(any(target_os = \"macos\", windows, target_os = \"linux\"))".dependencies]
tauri-plugin-single-instance = { version = "2.0.0", features = ["deep-link"] }
