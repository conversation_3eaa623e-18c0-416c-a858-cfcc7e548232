import { Redirect, Route, Switch } from 'wouter'
import './App.css'
import 'mind-elixir/style'
import { getCurrent, onOpenUrl } from '@tauri-apps/plugin-deep-link';
import MindmapEdit from './views/MindmapEdit'
import { ThemeProvider } from '@/components/theme-provider'
import Test from './views/FlexTest'
import Dashboard from './views/Dashboard'
import { Toaster } from './components/ui/sonner'
import MindmapRead from './views/MindmapShare'
import { useEffect } from 'react'
import { fetchUserInfo } from './api/cloud'
import { useUserInfoStore } from './store'
import { listen } from '@tauri-apps/api/event'
import { toast } from 'sonner'
import { useTranslation } from 'react-i18next'
import { reCheck } from './utils/check'
import { MindElixirData } from 'mind-elixir';
import { saveMap } from './api/map';
import { generateFileId } from './utils/common';
import { validateMindElixirData } from './utils/validateMindElixirData';
import { navigate } from 'wouter/use-browser-location'
import { getCurrentWindow } from '@tauri-apps/api/window';

const App = () => {
  const { setUserInfo } = useUserInfoStore()
  const { t } = useTranslation()
  const fetchData = async () => {
    const userInfo = await fetchUserInfo()
    setUserInfo(userInfo)
  }
  useEffect(() => {
    fetchData()
  }, [])
  useEffect(() => {
    const unlisten = listen<{
      token: string
    }>('login', async e => {
      console.log(e)
      const {
        payload: { token },
      } = e
      // console.log(token)
      localStorage.setItem('token', token)
      await fetchData()
      await reCheck()
      toast.success(t('auth.loginSuccess'))
    })

    const unlisten2 = onOpenUrl(async(urls) => {
      console.log('deep link:', urls);
      const win = getCurrentWindow();
      console.log(win)
      await win.show();
      await win.setFocus();
    });

    const unlisten3 = listen<{
      mindmap: string
      source: string,
    }>('create-mindmap', async e => {
      // 插件打开 me 然后创建脑图
      const {
        payload: { mindmap, source },
      } = e
      const data: MindElixirData = JSON.parse(mindmap)
      console.log(data)
      // 统一验证数据格式
      const validation = validateMindElixirData(data)
      if (!validation.isValid) {
        throw new Error(`数据格式验证失败: ${validation.error}`)
      }

      const id = generateFileId()
      await saveMap(id, {
        content: data,
        id,
        createdAt: +Date.now(),
        updatedAt: +Date.now(),
        source,
      })
      navigate('/edit?id=' + id)
    })

    getCurrent()
      .then((urls) => {
        console.log("Initial URLs:", urls);
      })
      .catch((err) => {
        console.error("Failed to get initial URLs:", err);
      });

    return () => {
      unlisten.then(unlisten => {
        unlisten()
      })
      unlisten2.then(unlisten => {
        unlisten()
      })
      unlisten3.then(unlisten => {
        unlisten()
      })
    }
  }, [])
  return (
    <ThemeProvider defaultTheme="dark" storageKey="vite-ui-theme">
      {/* <Route path="/about">About Us</Route> */}

      {/*
      Routes below are matched exclusively -
      the first matched route gets rendered
    */}
      <Switch>
        <Route path="/">
          <Redirect to="/maps" />
        </Route>
        <Route path="/edit" component={MindmapEdit} />
        <Route path="/share">
          <MindmapRead type="share" />
        </Route>
        <Route path="/trash">
          <MindmapRead type="trash" />
        </Route>
        <Route path="/test" component={Test} />
        <Route path="/*">
          {/* <Link href="/edit">Editor</Link>
        <ModeToggle /> */}
          <Dashboard />
        </Route>
        {/* Default route in a switch */}
        <Route>404: No such page!</Route>
      </Switch>
      <Toaster richColors visibleToasts={1} />
    </ThemeProvider>
  )
}

export default App
