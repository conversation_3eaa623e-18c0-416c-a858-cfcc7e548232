{"common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "search": "Search", "searchMindMap": "Search Mind Map...", "searchTags": "Search Tags...", "tags": "Tags", "allTags": "All Tags", "noTagsFound": "No tags found.", "noMapsWithTag": "No mind maps with this tag.", "clearFilter": "Clear Filter", "back": "Back", "system": "System", "email": "Email", "username": "Username", "avatar": "Avatar", "password": "Password", "logout": "Logout", "dashboard": "Dashboard", "recycleBin": "Recycle Bin", "noData": "No Data", "recycleBinEmpty": "The recycle bin is empty.", "templatesEmpty": "No templates available.", "emptyRecycleBin": "Empty Recycle Bin", "preview": "Preview", "restore": "Rest<PERSON>", "deletePermanently": "Delete permanently", "lastEdited": "Last edited", "upload": "Upload", "file": "File", "link": "Link", "selectFile": "Select file", "enterLink": "Enter link", "import": "Import", "download": "Download", "clipboard": "Clipboard", "pasteJson": "Paste JSON", "dataCorrupted": "Data Corrupted", "cannotPreview": "Cannot preview", "error": "Error", "insert": "Insert"}, "mindmap": {"newMap": "Create New", "importMap": "Import", "importMapDescription": "Import a mind map from a JSON file or URL", "openMap": "Open Mind Map", "addNode": "Add Node", "deleteNode": "Delete Node", "addChild": "Add Child Node", "addSibling": "Add Sibling Node", "centerMap": "Center Map", "zoomIn": "Zoom In", "zoomOut": "Zoom Out", "mindMap": "Mind Map", "outline": "Outline", "saveAsNewMap": "Save as New Map", "mindMapUpdated": "Mind map updated", "mindMapUploaded": "Mind map uploaded", "outlineMode": "Outline Mode", "saved": "Saved", "importFailed": "Import failed", "savedLocally": "Saved locally", "newMindMap": "New Mind Map", "insertJson": "Insert JSON", "insertJsonDescription": "Paste JSON data to replace current node or add child nodes", "jsonInsertHint": "If JSON contains id and topic properties, it will replace the current node; if it's an array, it will be added as child nodes"}, "nodeMenu": {"general": "General", "note": "Note", "image": "Image", "selectNode": "Please select a node.", "globalSettings": "Global Settings", "mindMapSettings": "Mind Map Settings", "theme": "Theme", "source": "Source", "fileInfo": "File Info", "createdAt": "Created At", "updatedAt": "Updated At", "enterSource": "Enter source (e.g., video URL)"}, "generalNodeSettings": {"color": "Color", "backgroundColor": "Background Color", "fontSize": "Font Size", "tags": "Tags", "tagsDescription": "Separate tags with commas.", "icons": "Icons", "url": "URL", "urlDescription": "Add a hyperlink to this node.", "clear": "Clear"}, "imageSelector": {"imageUrl": "Image URL", "fetchImage": "Fetch Image", "widthHeight": "Width & Height", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "objectFit": "Object Fit", "clearImage": "Clear Image"}, "tools": {"fullscreen": "Full Screen", "center": "Center", "zoomout": "Zoom Out", "zoomin": "Zoom In", "left": "Left", "right": "Right", "side": "Side", "minimap": "Mini Map"}, "contextMenu": {"ai": "AI Create", "aiWithPrompt": "AI Create With Prompt", "generate": "Generate!", "prompt": "Prompt", "addChild": "Add Child", "addParent": "Add Parent", "addSibling": "Add Sibling", "remove": "Remove", "focus": "Focus", "unfocus": "Unfocus", "moveUp": "Move Up", "moveDown": "Move Down", "link": "Link", "summary": "Summary"}, "dialog": {"confirmDeletion": "Confirm Deletion", "clearRecycleBinConfirmation": "Are you sure you want to clear the recycle bin? This action cannot be undone.", "limitReached": "Limit Reached", "subscriptionRequired": "You have created more than 12 mind maps. Please subscribe to unlock unlimited access and create more.", "maybeLater": "Maybe Later", "subscribeNow": "Subscribe Now", "unsavedChangesAlert": "You have unsaved changes. Are you sure you want to exit?"}, "operation": {"addChild": "Add Child", "addParent": "Add Parent", "addSibling": "Add Sibling", "remove": "Remove", "focus": "Focus", "unfocus": "Unfocus", "moveUp": "Move Up", "moveDown": "Move Down", "link": "Link", "linkTips": "Please click the node to link", "summary": "Summary", "moreActions": "More Actions", "singleNodeSelected": "One node selected", "multipleNodesSelected": "{{count}} nodes selected"}, "menu": {"mindElixir": "Mind Elixir", "open": "Open", "export": "Export", "theme": "Theme", "appearance": "Appearance", "system": "System", "light": "Light", "dark": "Dark", "shortcuts": "Shortcuts", "exit": "Exit"}, "shortcuts": {"action": "Action", "shortcut": "Shortcut", "displaySide": "Display side", "displayLeft": "<PERSON><PERSON><PERSON> left", "displayRight": "Display right", "save": "Save", "undo": "Undo", "copy": "Copy", "paste": "Paste", "cut": "Cut", "moveToCenter": "Move to center", "editTopic": "Edit Topic", "zoomIn": "Zoom in", "zoomOut": "Zoom out", "resetZoom": "Reset zoom", "toggleNodeMenu": "Toggle Node Menu", "showShortcutsDialog": "Show Shortcuts Dialog", "toggleOutlineMode": "Toggle Outline Mode", "hint": "Press {{key}} to show this dialog again"}, "settings": {"title": "Settings", "theme": "Theme", "light": "Light", "dark": "Dark", "account": {"title": "Account", "changeEmail": "Change Email", "changePassword": "Change Password", "pleaseLogin": "<PERSON><PERSON>", "logout": "Logout", "userProfile": "User Profile", "upgradeToPro": "Upgrade to Pro", "checkLicense": "Check License", "githubLogin": "<PERSON><PERSON><PERSON>", "googleLogin": "Google Login"}, "appearance": {"title": "Appearance", "darkMode": "Dark Mode", "darkModeDescription": "Switch between light and dark mode", "theme": "Theme", "selectTheme": "Select a theme", "light": "Light", "dark": "Dark", "system": "System"}, "proxy": {"title": "Proxy", "enable": "Enable Proxy", "url": "Proxy URL"}, "ai": {"title": "AI Settings", "mode": "AI Mode", "geminiKey": "Gemini API Key", "openaiKey": "OpenAI API Key", "openaiModel": "OpenAI Model", "baseUrl": "Base URL", "aiProvider": "AI Provider", "normal": "Normal", "contextAware": "Context-Aware Expansion", "openaiCompatible": "OpenAI Compatible", "selectModel": "Select Model", "loading": "Loading models...", "errorFetchingModels": "Error fetching models", "searchModel": "Search model...", "noModelFound": "No model found.", "geminiModel": "Gemini Model", "checkSettings": "Please check AI settings", "gemini": "Gemini", "openai": "OpenAI", "manualInputRequired": "Unable to fetch model list, please enter manually", "aiChildren": "AI Generate Children", "aiChildrenDescription": "Use AI to generate child nodes for the current node", "promptPlaceholder": "Enter a prompt to describe what you want to generate...", "generate": "Generate"}, "notifications": {"title": "Notifications", "emailNotifications": "Email Notifications", "emailDescription": "Receive email about your activity", "pushNotifications": "Push Notifications", "pushDescription": "Get notified about new features"}, "language": {"title": "Language", "preferred": "Preferred Language", "selectLanguage": "Select a language", "select": "Select Language"}, "privacy": {"title": "Privacy", "publicProfile": "Public Profile", "publicProfileDescription": "Make your mind maps visible to others", "shareAnalytics": "Share Analytics", "shareAnalyticsDescription": "Help us improve our product"}, "storage": {"title": "Data Storage", "openDataFolder": "Open Data Folder", "openDataFolderDescription": "Open the folder where your mind maps are stored"}, "about": {"title": "About", "version": "Version"}}, "auth": {"loginSuccess": "Login Success", "logoutSuccess": "Logout Success"}, "error": {"general": "An error occurred", "importFailed": "Import failed, possibly due to format error", "noNodeSelected": "Please select a node first", "invalidJson": "Invalid JSON format", "invalidJsonFormat": "Invalid JSON format, please ensure it's valid node data or child nodes array", "jsonInsertFailed": "JSON insert failed", "emptyJson": "JSON content cannot be empty", "unauthorized": "<PERSON>gin expired, please login again"}, "import": {"jsonFileDescription": "Import JSON file exported from Mind Elixir.", "fileDescriptionBatch": "Supports batch import of JSON and XMind files", "linkDescription": "Import from Mind Elixir Cloud", "clipboardDescription": "Paste JSON content", "batchTags": "Batch Tags", "batchTagsPlaceholder": "Enter tags, separated by commas", "batchTagsDescription": "Add same tags to all imported files for easier categorization in the tags view", "batchImportSuccess": "Successfully imported {{count}} files", "batchImportPartialSuccess": "Successfully imported {{success}} files, {{fail}} files failed to import", "batchImportFailed": "Batch import failed, all files could not be imported"}, "search": {"placeholder": "Search in mindmap...", "noResults": "No results found", "results": "{{current}}/{{total}} results"}, "tips": {"pressF1ToCenter": "Press F1 to return to mindmap center"}, "success": {"jsonInserted": "JSON data inserted successfully"}}