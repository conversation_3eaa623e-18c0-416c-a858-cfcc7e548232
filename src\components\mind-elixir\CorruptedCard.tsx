import { Alert<PERSON><PERSON>gle, MoreH<PERSON>zon<PERSON> } from 'lucide-react'
import { Button } from '../ui/button'
import { useTranslation } from 'react-i18next'
import { MindElixirLoadError, deleteMap, deleteTrash, restoreMap } from '@/api/map'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '../ui/dropdown-menu'

interface CorruptedCardProps {
  map: MindElixirLoadError
  onDone: () => void
  type: 'map' | 'trash'
}

const CorruptedCard = ({ map, onDone, type }: CorruptedCardProps) => {
  const { t } = useTranslation()

  const handleDelete = async () => {
    if (type === 'trash') {
      await deleteTrash(map.id)
    } else {
      await deleteMap(map.id)
    }
    onDone()
  }

  return (
    <div className="relative border rounded-md overflow-hidden bg-background group border-destructive/50">
      <div className="absolute top-1 right-1 z-10">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="h-6 w-6 bg-destructive/10 dark:bg-destructive/20" onClick={e => e.stopPropagation()}>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {type === 'trash' ? (
              <>
                <DropdownMenuItem
                  onClick={async e => {
                    e.stopPropagation()
                    await restoreMap(map.id)
                    onDone()
                  }}
                >
                  {t('common.restore')}
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={async e => {
                    e.stopPropagation()
                    await handleDelete()
                  }}
                >
                  {t('common.deletePermanently')}
                </DropdownMenuItem>
              </>
            ) : (
              <DropdownMenuItem
                onClick={async e => {
                  e.stopPropagation()
                  await handleDelete()
                }}
              >
                {t('common.delete')}
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="aspect-square bg-destructive/5 flex items-center justify-center">
        <div className="text-center p-4">
          <AlertTriangle className="h-8 w-8 text-destructive mx-auto mb-2" />
          <p className="text-sm text-destructive font-medium">{t('common.dataCorrupted')}</p>
          <p className="text-xs text-muted-foreground mt-1">{t('common.cannotPreview')}</p>
        </div>
      </div>

      <div className="p-2 bg-destructive/5">
        <h3 className="font-medium truncate text-destructive">{map.id}</h3>
        <p className="text-xs text-destructive/70">
          {t('common.error')}: {map.error}
        </p>
      </div>
    </div>
  )
}

export default CorruptedCard
