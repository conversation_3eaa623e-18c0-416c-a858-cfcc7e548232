import { defineConfig } from 'vite'
import path from 'path'
import react from '@vitejs/plugin-react-swc'

const host = process.env.TAURI_DEV_HOST
const isDev = process.env.TAURI_ENV_DEBUG
console.log('host', host, 'isDev', isDev)
// https://vitejs.dev/config/
export default defineConfig({
  // prevent vite from obscuring rust errors
  clearScreen: false,
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    // <PERSON><PERSON> expects a fixed port, fail if that port is not available
    strictPort: true,
    // if the host <PERSON><PERSON> is expecting is set, use it
    host: host || false,
    port: 5174,
    // proxy: {
    //   '/api': {
    //     target: 'https://mind-elixir-backend.fly.dev/',
    //     changeOrigin: true,
    //     // rewrite: (path) => path.replace(/^\/api/, ""),
    //   },
    // },
  },
  // Env variables starting with the item of `envPrefix` will be exposed in tauri's source code through `import.meta.env`.
  envPrefix: ['VITE_', 'TAURI_ENV_'],
  build: {
    // Tauri uses Chromium on Windows and WebKit on macOS and Linux
    target: process.env.TAURI_ENV_PLATFORM == 'windows' ? 'chrome105' : 'safari13',
    // don't minify for debug builds
    minify: !isDev ? 'esbuild' : false,
    // produce sourcemaps for debug builds
    sourcemap: !!isDev,
  },
})
