export default function debounce(func: Function, wait: number) {
  let timeout = 0
  return function (this: any, ...args: any[]) {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func.apply(this, args), wait)
  }
}

let lastTime = 0
export function throttle(fn: Function, delay: number) {
  return function (...args) {
    const now = Date.now()
    if (now - lastTime >= delay) {
      console.log('really call')
      lastTime = now
      fn.apply(this, args)
    }
  }
}
