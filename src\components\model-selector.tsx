import { listModel } from '@/api/ai'
import { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { Label } from 'recharts'
import { Input } from './ui/input'
import { cn } from '@/lib/utils'
import { Command, CommandInput, CommandList, CommandEmpty, CommandGroup, CommandItem } from './ui/command'
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover'
import { ChevronsUpDown, Check } from 'lucide-react'
import { Button } from './ui/button'

export const ModelSelector = ({
  baseUrl,
  apiKey,
  value,
  onChange,
}: {
  baseUrl: string
  apiKey: string
  value: string
  onChange: (model: string) => void
}) => {
  const { t } = useTranslation()
  const [open, setOpen] = useState(false)
  const [models, setModels] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchModels = async () => {
      setLoading(true)
      setError(null)
      try {
        const response = await listModel(baseUrl, apiKey)
        console.log(response)
        if (response.status === 200) {
          const data = (await response.json()) as {
            data: {
              id: string
              object: string
              owned_by: string
            }[]
          }
          console.log(data)
          setModels(data.data.map(model => model.id))
        } else {
          setError(t('settings.ai.errorFetchingModels'))
        }
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (err) {
        setError(t('settings.ai.errorFetchingModels'))
      } finally {
        setLoading(false)
      }
    }

    fetchModels()
  }, [baseUrl, apiKey])

  return (
    <div className="space-y-2">
      <Label>{t('settings.ai.selectModel')}</Label>
      {loading && <p>{t('settings.ai.loading')}</p>}
      {error && (
        <>
          <Input value={value} onChange={e => onChange(e.target.value)} />
          <p>{t('settings.ai.manualInputRequired')}</p>
        </>
      )}
      {!loading && !error && (
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button variant="outline" role="combobox" aria-expanded={open} className="w-full justify-between">
              {value ? models.find(model => model === value) : t('settings.ai.selectModel')}
              <ChevronsUpDown className="opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="PopoverContent p-0">
            <Command
              filter={(value, search) => {
                if (value.toLowerCase().includes(search.toLowerCase())) return 1
                return 0
              }}
            >
              <CommandInput placeholder={t('settings.ai.searchModel')} className="h-9" />
              <CommandList>
                <CommandEmpty>{t('settings.ai.noModelFound')}</CommandEmpty>
                <CommandGroup>
                  {models.map(model => (
                    <CommandItem
                      key={model}
                      value={model}
                      onSelect={currentValue => {
                        onChange(currentValue === value ? '' : currentValue)
                        setOpen(false)
                      }}
                    >
                      {model}
                      <Check className={cn('ml-auto', value === model ? 'opacity-100' : 'opacity-0')} />
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      )}
    </div>
  )
}
