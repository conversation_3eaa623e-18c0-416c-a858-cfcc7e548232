import MindElixir, { MindElixirInstance, NodeObj } from 'mind-elixir'
import { useEffect, useRef, useState } from 'react'
import ToolButtonGroup from '@/components/mind-elixir/ToolButtonGroup'
import { useTheme } from '@/components/theme-provider'
import MiniMap from '@/components/mind-elixir/MiniMap'
import { Button } from '@/components/ui/button'
import { BrainCircuit, DownloadCloud, LucideChevronLeft, TableOfContents } from 'lucide-react'
import { useMindElixirStore } from '@/store'
import { loadMap, MindElixirLocal, saveMap } from '@/api/map'
import { useLocation } from 'wouter'
import { fetchShared } from '@/api/cloud'
import { OutlinerMode } from '@/components/app/OutlinerMode'
import { generateFileId } from '@/utils/common'
import { toast } from 'sonner'
import { useTranslation } from 'react-i18next'

const store = useMindElixirStore.getState()
const MindmapRead = ({ type }: { type: 'share' | 'trash' }) => {
  const [, navigate] = useLocation()
  const context = useTheme()
  const [mode, setMode] = useState<'map' | 'outline'>('map')
  const [, forceUpdate] = useState(0)
  const meiRef = useRef<MindElixirInstance>(undefined)
  const localRef = useRef<MindElixirLocal>(undefined)
  const elRef = useRef<HTMLDivElement>(null)
  const [outlinerData, setOutlinerData] = useState<NodeObj[]>([])
  const { t } = useTranslation()
  
  useEffect(() => {
    const params = new URLSearchParams(window.location.search)
    const id = params.get('id')!
    let theme = context.theme
    if (context.theme === 'system') theme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
    const mei = new MindElixir({
      theme: theme === 'dark' ? MindElixir.DARK_THEME : MindElixir.THEME,
      el: elRef.current!,
      contextMenu: false,
      toolBar: false,
      allowUndo: false,
      editable: false,
      draggable: false,
    })

    mei.container.addEventListener('contextmenu', e => {
      e.preventDefault()
    })
    meiRef.current = mei
    store.setMei(mei)
    forceUpdate(prev => prev + 1)
    const init = async () => {
      try {
        let data: MindElixirLocal | null
        if (type === 'share') {
          data = await fetchShared(id)
        } else {
          data = await loadMap('trash', id!)
        }
        console.log(data)
        if (data) {
          localRef.current = data
          mei.init(data.content)
        } else {
          mei.init(MindElixir.new('New Mind Map'))
        }
      } catch (e) {
        console.log('Div had been cleared.', e)
      }
    }
    init()

    return () => {
      mei.destroy()
      store.setMei(undefined)
    }
  }, [])
  return (
    <div className="relative h-screen">
      {meiRef.current && (
        <div className="absolute flex gap-3 top-3 left-3 z-50">
          <Button
            variant="outline"
            onClick={() => {
              window.history.back()
            }}
          >
            <LucideChevronLeft />
          </Button>
          <Button
            variant="outline"
            onClick={() => {
              if (mode === 'map') {
                setMode('outline')
                const data = meiRef.current!.getData()
                setOutlinerData(data.nodeData.children!)
              } else {
                setMode('map')
                meiRef.current!.nodeData.children = outlinerData
                meiRef.current!.refresh()
              }
            }}
          >
            {mode === 'map' ? <BrainCircuit /> : <TableOfContents />}
          </Button>
          {type === 'share' && (
            <Button
              variant="outline"
              onClick={async () => {
                const id = generateFileId()
                await saveMap(id, {
                  content: meiRef.current!.getData(),
                  id,
                  createdAt: +Date.now(),
                  updatedAt: +Date.now(),
                })
                navigate('/edit?id=' + id, {
                  replace: true,
                })
                toast.success(t('mindmap.savedLocally'))
              }}
            >
              <DownloadCloud />
            </Button>
          )}
        </div>
      )}
      {/* style={{ height: 'calc(100vh - 36px)' }} */}

      <div className="relative h-full w-full">
        <div ref={elRef} id="map" className="h-full"></div>
        <MiniMap />
        <div className="absolute left-4 bottom-4 flex flex-col">
          <ToolButtonGroup />
        </div>
      </div>

      {mode === 'outline' && (
        <OutlinerMode mei={meiRef.current} outlinerData={outlinerData} setOutlinerData={setOutlinerData} readonly />
      )}
    </div>
  )
}

export default MindmapRead
