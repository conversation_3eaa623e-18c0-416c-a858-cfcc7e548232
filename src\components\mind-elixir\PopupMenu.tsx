import { createRoot, Root } from 'react-dom/client'
import { But<PERSON> } from '../ui/button'
import MindElixir, { MindElixirInstance, NodeObj } from 'mind-elixir'
import { ColorPicker } from '../unofficial/color-picker'
import { useMemo, useState } from 'react'
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover'
import { Input } from '../ui/input'

let currentNode: NodeObj | undefined = undefined
let currentMei: MindElixirInstance | undefined = undefined
const PopupMenu = () => {
  const [, forceUpdate] = useState(0)
  const node = currentNode!

  const tpcStyle = useMemo(() => {
    const tpc = MindElixir.E(currentNode!.id)
    return getComputedStyle(tpc)
  }, [currentNode])

  const onNodeUpdate = <K extends keyof NodeObj>(key: K, value: NodeObj[K]) => {
    node[key] = value
    currentMei!.reshapeNode(MindElixir.E(node!.id), node)
    forceUpdate(prev => prev + 1)
  }
  return (
    <div className="flex relative" onClick={e => e.stopPropagation()}>
      <Popover>
        <PopoverTrigger asChild>
          <Button>Url</Button>
        </PopoverTrigger>
        <PopoverContent className="w-80" side="top">
          <Input placeholder="Url" value={node.hyperLink || ''} onChange={e => onNodeUpdate('hyperLink', e.target.value)} />
          <div className="text-gray-500 mt-2">Add a hyperlink to this node.</div>
        </PopoverContent>
      </Popover>
      <Popover>
        <PopoverTrigger asChild>
          <Button>Tags</Button>
        </PopoverTrigger>
        <PopoverContent className="w-80" side="top">
          <Input
            placeholder="Add Tags"
            value={node.tags?.join(',') || ''}
            onChange={e => onNodeUpdate('tags', e.target.value ? e.target.value.split(',') : undefined)}
          />
          <div className="text-gray-500 mt-2">Separate tags with commas.</div>
        </PopoverContent>
      </Popover>
      <ColorPicker side="top" value={node.style?.color || tpcStyle.color} onChange={color => onNodeUpdate('style', { ...node.style, color })} />
      <ColorPicker
        side="top"
        value={node!.style?.background || tpcStyle.backgroundColor}
        onChange={background => {
          onNodeUpdate('style', { ...node!.style, background })
        }}
      />
    </div>
  )
}

let menu: HTMLDivElement | null = null
let root: Root | null = null
const getPopup = () => {
  if (menu)
    return {
      menu,
      root,
    }
  else {
    menu = document.createElement('div')
    menu.style.userSelect = 'auto'
    root = createRoot(menu)
    return { menu, root }
  }
}

export const showPopup = (mei: MindElixirInstance, node: NodeObj) => {
  currentNode = node
  currentMei = mei
  const tpc = MindElixir.E(node.id)
  const tpcRect = tpc.getBoundingClientRect()
  const container = mei.container
  const containderRect = container.getBoundingClientRect()
  const x = tpcRect.x - containderRect.x
  const y = tpcRect.y - containderRect.y
  const { menu, root } = getPopup()!
  menu.style.position = 'absolute'
  menu.style.top = `${y - 40}px`
  menu.style.left = `${x}px`
  root!.render(<PopupMenu />)
  container.appendChild(menu)
}

export const hidePopup = () => {
  if (menu) {
    menu.remove()
  }
}

// export default PopupMenu
