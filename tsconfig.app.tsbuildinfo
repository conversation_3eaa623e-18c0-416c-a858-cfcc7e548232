{"root": ["./src/app.tsx", "./src/main.tsx", "./src/store.ts", "./src/vite-env.d.ts", "./src/api/ai.ts", "./src/api/map.ts", "./src/components/mode-toggle.tsx", "./src/components/theme-provider.tsx", "./src/components/app/maplist.tsx", "./src/components/app/templatelist.tsx", "./src/components/app/trashlist.tsx", "./src/components/app/usersettings.tsx", "./src/components/mind-elixir/contextmenu.tsx", "./src/components/mind-elixir/imageselector.tsx", "./src/components/mind-elixir/minimap.tsx", "./src/components/mind-elixir/nodemenu.tsx", "./src/components/mind-elixir/popupmenu.tsx", "./src/components/mind-elixir/previewcard.tsx", "./src/components/mind-elixir/shortcuttable.tsx", "./src/components/mind-elixir/templatecard.tsx", "./src/components/mind-elixir/toolbuttongroup.tsx", "./src/components/mind-elixir/topmenu.tsx", "./src/components/react-outliner/outliner.tsx", "./src/components/react-outliner/dom.ts", "./src/components/ui/accordion.tsx", "./src/components/ui/alert-dialog.tsx", "./src/components/ui/alert.tsx", "./src/components/ui/aspect-ratio.tsx", "./src/components/ui/avatar.tsx", "./src/components/ui/badge.tsx", "./src/components/ui/breadcrumb.tsx", "./src/components/ui/button.tsx", "./src/components/ui/calendar.tsx", "./src/components/ui/card.tsx", "./src/components/ui/carousel.tsx", "./src/components/ui/chart.tsx", "./src/components/ui/checkbox.tsx", "./src/components/ui/collapsible.tsx", "./src/components/ui/command.tsx", "./src/components/ui/context-menu.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/drawer.tsx", "./src/components/ui/dropdown-menu.tsx", "./src/components/ui/form.tsx", "./src/components/ui/hover-card.tsx", "./src/components/ui/input-otp.tsx", "./src/components/ui/input.tsx", "./src/components/ui/label.tsx", "./src/components/ui/menubar.tsx", "./src/components/ui/navigation-menu.tsx", "./src/components/ui/pagination.tsx", "./src/components/ui/popover.tsx", "./src/components/ui/progress.tsx", "./src/components/ui/radio-group.tsx", "./src/components/ui/resizable.tsx", "./src/components/ui/scroll-area.tsx", "./src/components/ui/select.tsx", "./src/components/ui/separator.tsx", "./src/components/ui/sheet.tsx", "./src/components/ui/skeleton.tsx", "./src/components/ui/slider.tsx", "./src/components/ui/sonner.tsx", "./src/components/ui/switch.tsx", "./src/components/ui/table.tsx", "./src/components/ui/tabs.tsx", "./src/components/ui/textarea.tsx", "./src/components/ui/toast.tsx", "./src/components/ui/toaster.tsx", "./src/components/ui/toggle-group.tsx", "./src/components/ui/toggle.tsx", "./src/components/ui/tooltip.tsx", "./src/components/unofficial/color-picker.tsx", "./src/components/unofficial/input-tags.tsx", "./src/hooks/use-toast.ts", "./src/i18n/config.ts", "./src/lib/utils.ts", "./src/svg/addchild.tsx", "./src/svg/addparent.tsx", "./src/svg/addsibling.tsx", "./src/svg/center.tsx", "./src/svg/fullscreen.tsx", "./src/svg/lefticon.tsx", "./src/svg/righticon.tsx", "./src/svg/sideicon.tsx", "./src/svg/zoomin.tsx", "./src/svg/zoomout.tsx", "./src/templates/index.ts", "./src/theme/bw.ts", "./src/theme/dopamine.ts", "./src/theme/dracula.ts", "./src/theme/tokyo-night.ts", "./src/utils/debounce.ts", "./src/utils/mind-elixir.ts", "./src/utils/operationmap.ts", "./src/views/dashboard.tsx", "./src/views/flextest.tsx", "./src/views/mindmapedit.tsx"], "errors": true, "version": "5.6.3"}