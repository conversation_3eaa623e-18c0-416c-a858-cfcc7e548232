import { useState, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '../ui/dialog'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '../ui/tabs'
import { Button } from '../ui/button'
import { generateFileId } from '@/utils/common'
import { saveMap } from '@/api/map'
import type { MindElixirData } from 'mind-elixir'
import { navigate } from 'wouter/use-browser-location'
import { fetchShared } from '@/api/cloud'
import { toast } from 'sonner'
import { Loader2, SquareArrowOutUpRight } from 'lucide-react'
import { Textarea } from '../ui/textarea'
import { validateMindElixirData } from '@/utils/validateMindElixirData'

import { importXMindFile, convertXmindToMindElixir } from '@mind-elixir/import-xmind'

const linkExample = 'https://cloud.mind-elixir.com/en/share/675edaf6ebd56699ad0d261d'

export const ImportDialog = ({ open, setOpen }: { open: boolean; setOpen: (open: boolean) => void }) => {
  const { t } = useTranslation()
  const [link, setLink] = useState('')
  const [clipboardText, setClipboardText] = useState('')
  const [activeTab, setActiveTab] = useState<string>('file')
  const [batchTags, setBatchTags] = useState('')
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleImportFromLink = async () => {
    const id = link.split('/').at(-1)
    const data = await fetchShared(id!)
    return data.content
  }

  const handleImportXmind = async (): Promise<MindElixirData[]> => {
    if (!fileInputRef.current?.files?.length) {
      return []
    }

    const files = Array.from(fileInputRef.current.files)
    const results: MindElixirData[] = []

    for (const file of files) {
      try {
        const sheets = await importXMindFile(file)
        // Convert to MindElixir format
        const mindElixirData = convertXmindToMindElixir(sheets[0])
        results.push(mindElixirData)
      } catch (error) {
        console.error(`Failed to parse XMind file ${file.name}:`, error)
      }
    }

    return results.filter(Boolean)
  }

  const handleImportFromFile = (): Promise<MindElixirData[]> => {
    return new Promise(resolve => {
      if (fileInputRef.current?.files?.length) {
        const files = Array.from(fileInputRef.current.files)
        const results: MindElixirData[] = []
        let completed = 0

        files.forEach((file, index) => {
          const reader = new FileReader()
          reader.onload = async e => {
            const res = e.target?.result
            try {
              let data: MindElixirData | { content: MindElixirData } = JSON.parse(res as string)
              if ('content' in data) {
                // for mind elixir cloud
                data = data.content
              }
              results[index] = data
            } catch (error) {
              console.error(`Failed to parse file ${file.name}:`, error)
              // Skip invalid files but continue processing others
            }

            completed++
            if (completed === files.length) {
              // Filter out undefined results from failed files
              resolve(results.filter(Boolean))
            }
          }
          reader.onerror = () => {
            completed++
            if (completed === files.length) {
              resolve(results.filter(Boolean))
            }
          }
          reader.readAsText(file)
        })
      } else {
        resolve([])
      }
    })
  }

  const handleImportFromClipboard = (): Promise<MindElixirData | null> => {
    return new Promise((resolve, reject) => {
      if (!clipboardText.trim()) {
        resolve(null)
        return
      }

      try {
        let data: MindElixirData | { content: MindElixirData } = JSON.parse(clipboardText)
        if ('content' in data) {
          // for mind elixir cloud
          data = data.content
        }
        resolve(data)
      } catch (error) {
        reject(error)
      }
    })
  }

  const [isLoading, setIsLoading] = useState(false)
  const handleImport = async () => {
    setIsLoading(true)
    try {
      if (activeTab === 'file') {
        let dataArray: MindElixirData[] = []

        // Check if we have any xmind files
        const files = Array.from(fileInputRef.current?.files || [])
        const xmindFiles = files.filter(file => file.name.toLowerCase().endsWith('.xmind'))
        const jsonFiles = files.filter(file => file.name.toLowerCase().endsWith('.json'))

        // Handle xmind files
        if (xmindFiles.length > 0) {
          const xmindData = await handleImportXmind()
          dataArray = [...dataArray, ...xmindData]
        }

        // Handle json files
        if (jsonFiles.length > 0) {
          const jsonData = await handleImportFromFile()
          dataArray = [...dataArray, ...jsonData]
        }

        if (dataArray.length === 0) {
          setIsLoading(false)
          return toast.error(t('mindmap.importFailed'))
        }

        let successCount = 0
        let failCount = 0
        let lastSuccessfulId = ''

        // 批量处理文件
        for (const data of dataArray) {
          try {
            // 统一验证数据格式
            const validation = validateMindElixirData(data)
            if (!validation.isValid) {
              throw new Error(`数据格式验证失败: ${validation.error}`)
            }
            // 为批量导入文件的 Root 批量添加同一 tags
            if (batchTags.trim()) {
              const tagsArray = batchTags.split(',').map(tag => tag.trim()).filter(tag => tag)
              if (tagsArray.length > 0) {
                if (data.nodeData.tags) {
                  data.nodeData.tags.push(...tagsArray)
                } else {
                  data.nodeData.tags = tagsArray
                }
              }
            }
            const id = generateFileId()
            await saveMap(id, {
              content: data,
              id,
              createdAt: +Date.now(),
              updatedAt: +Date.now(),
            })
            successCount++
            lastSuccessfulId = id
          } catch (error) {
            console.error('Failed to import file:', error)
            failCount++
          }
        }

        setIsLoading(false)

        if (successCount > 0) {
          if (failCount > 0) {
            toast.success(t('import.batchImportPartialSuccess', { success: successCount, fail: failCount }))
          } else {
            toast.success(t('import.batchImportSuccess', { count: successCount }))
          }

          // 如果只导入了一个文件，直接跳转到编辑页面
          if (successCount === 1 && dataArray.length === 1) {
            navigate('/edit?id=' + lastSuccessfulId)
          }
        } else {
          toast.error(t('import.batchImportFailed'))
        }

        setOpen(false)
      } else if (activeTab === 'link') {
        const data = await handleImportFromLink()
        if (!data) {
          setIsLoading(false)
          return toast.error(t('mindmap.importFailed'))
        }

        // 统一验证数据格式
        const validation = validateMindElixirData(data)
        if (!validation.isValid) {
          throw new Error(`数据格式验证失败: ${validation.error}`)
        }

        const id = generateFileId()
        await saveMap(id, {
          content: data,
          id,
          createdAt: +Date.now(),
          updatedAt: +Date.now(),
        })
        navigate('/edit?id=' + id)
        setOpen(false)
        setIsLoading(false)
      } else if (activeTab === 'clipboard') {
        const data = await handleImportFromClipboard()
        if (!data) {
          setIsLoading(false)
          return toast.error(t('mindmap.importFailed'))
        }

        // 统一验证数据格式
        const validation = validateMindElixirData(data)
        if (!validation.isValid) {
          throw new Error(`数据格式验证失败: ${validation.error}`)
        }

        const id = generateFileId()
        await saveMap(id, {
          content: data,
          id,
          createdAt: +Date.now(),
          updatedAt: +Date.now(),
        })
        navigate('/edit?id=' + id)
        setOpen(false)
        setIsLoading(false)
      }
    } catch (error) {
      console.dir(error)
      toast.error(t('error.importFailed'))
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t('mindmap.importMap')}</DialogTitle>
          <DialogDescription>{t('mindmap.importMapDescription')}</DialogDescription>
        </DialogHeader>
        <Tabs defaultValue="file" className="w-full h-72" onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="file">{t('common.file')}</TabsTrigger>
            <TabsTrigger value="link">{t('common.link')}</TabsTrigger>
            <TabsTrigger value="clipboard">{t('common.clipboard')}</TabsTrigger>
          </TabsList>
          <TabsContent value="file" className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="file">{t('common.selectFile')}</Label>
              <Input id="file" type="file" accept=".json,.xmind" multiple ref={fileInputRef} />
              <div className="text-sm text-muted-foreground flex items-center gap-1">{t('import.fileDescriptionBatch')}</div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="batchTags">{t('import.batchTags')}</Label>
              <Input
                id="batchTags"
                placeholder={t('import.batchTagsPlaceholder')}
                value={batchTags}
                onChange={e => setBatchTags(e.target.value)}
              />
              <div className="text-sm text-muted-foreground">{t('import.batchTagsDescription')}</div>
            </div>
          </TabsContent>
          <TabsContent value="link" className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="link">{t('common.enterLink')}</Label>
              <Input id="link" placeholder={linkExample} value={link} onChange={e => setLink(e.target.value)} />
              <div className="text-sm text-muted-foreground flex items-center gap-1">
                {t('import.linkDescription')}
                <a className="inline-block" target="_blank" href="https://cloud.mind-elixir.com/#/list/public">
                  <SquareArrowOutUpRight size={16} />
                </a>
              </div>
            </div>
          </TabsContent>
          <TabsContent value="clipboard" className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="clipboard">{t('common.pasteJson')}</Label>
              <Textarea
                id="clipboard"
                placeholder='{"nodeData":{"id":"root","topic":"Mind Elixir",...}}'
                value={clipboardText}
                onChange={e => setClipboardText(e.target.value)}
                className="min-h-[150px] font-mono text-sm"
              />
              <div className="text-sm text-muted-foreground flex items-center gap-1">{t('import.clipboardDescription')}</div>
            </div>
          </TabsContent>
        </Tabs>
        <DialogFooter>
          <Button onClick={handleImport} disabled={isLoading}>
            {isLoading && <Loader2 className="animate-spin" />}
            {t('common.import')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
