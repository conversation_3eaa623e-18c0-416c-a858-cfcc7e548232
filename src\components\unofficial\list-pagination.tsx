import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination'

export function ListPagination({ total, page, size, setPage }: { total: number; page: number; size: number; setPage: (page: number) => void }) {
  const totalPages = Math.ceil(total / size)
  const renderPageNumbers = () => {
    const pages = []
    const range = 1 // 当前页前后显示的页数

    // 始终显示第一页
    pages.push(
      <PaginationItem
        key={1}
        onClick={() => {
          setPage(1)
        }}
      >
        <PaginationLink isActive={page === 1} className="h-8 w-8 sm:h-10 sm:w-10">{1}</PaginationLink>
      </PaginationItem>
    )

    // 如果当前页距离首页较远，显示省略号
    if (page - range > 2) {
      pages.push(
        <PaginationItem key="ellipsis1">
          <PaginationEllipsis />
        </PaginationItem>
      )
    }

    // 显示当前页附近的页码
    for (let i = Math.max(2, page - range); i <= Math.min(totalPages - 1, page + range); i++) {
      pages.push(
        <PaginationItem
          key={i}
          onClick={() => {
            setPage(i)
          }}
        >
          <PaginationLink isActive={page === i} className="h-8 w-8 sm:h-10 sm:w-10">{i}</PaginationLink>
        </PaginationItem>
      )
    }

    // 如果当前页距离末页较远，显示省略号
    if (page + range < totalPages - 1) {
      pages.push(
        <PaginationItem key="ellipsis2">
          <PaginationEllipsis />
        </PaginationItem>
      )
    }

    // 如果总页数大于1，始终显示最后一页
    if (totalPages > 1) {
      pages.push(
        <PaginationItem
          key={totalPages}
          onClick={() => {
            setPage(totalPages)
          }}
        >
          <PaginationLink isActive={page === totalPages} className="h-8 w-8 sm:h-10 sm:w-10">{totalPages}</PaginationLink>
        </PaginationItem>
      )
    }

    return pages
  }

  return (
    <Pagination className="select-none">
      {totalPages > 1 && (
        <PaginationContent className="w-full max-w-lg flex justify-between items-center p-2 sm:p-4">
          <PaginationItem>
            <PaginationPrevious
              onClick={() => {
                if (page > 1) {
                  setPage(page - 1)
                }
              }}
              className="h-8 px-2 sm:h-10 sm:px-4"
            />
          </PaginationItem>
          <div className="flex items-center gap-1 sm:gap-2">
            {renderPageNumbers()}
          </div>
          <PaginationItem>
            <PaginationNext
              onClick={() => {
                if (page < totalPages) {
                  setPage(page + 1)
                }
              }}
              className="h-8 px-2 sm:h-10 sm:px-4"
            />
          </PaginationItem>
        </PaginationContent>
      )}
    </Pagination>
  )
}
