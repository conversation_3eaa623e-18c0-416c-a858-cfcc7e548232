import { MindElixirInstance, Topic } from 'mind-elixir'
import i18n from '@/i18n/config'
import { createTips } from './mind-elixir'



export const operations = [
  // context menu
  {
    label: 'operation.addChild',
    defaultLabel: 'Add Child',
    shortcut: 'Tab',
    onClick(mei: MindElixirInstance) {
      mei.addChild()
    },
  },
  {
    label: 'operation.addParent',
    defaultLabel: 'Add Parent',
    shortcut: 'Ctrl + Enter',
    hideIfRoot: true,
    onClick(mei: MindElixirInstance) {
      mei.insertParent()
    },
  },
  {
    label: 'operation.addSibling',
    defaultLabel: 'Add Sibling',
    shortcut: 'Enter',
    hideIfRoot: true,
    onClick(mei: MindElixirInstance) {
      mei.insertSibling('after')
    },
  },
  {
    label: 'operation.remove',
    defaultLabel: 'Remove',
    shortcut: 'Delete',
    hideIfRoot: true,
    onClick(mei: MindElixirInstance) {
      mei.removeNodes(mei.currentNodes)
    },
  },
  {
    label: 'operation.focus',
    defaultLabel: 'Focus',
    onClick(mei: MindElixirInstance) {
      mei.focusNode(mei.currentNode as Topic)
    },
  },
  {
    label: 'operation.unfocus',
    defaultLabel: 'Unfocus',
    onClick(mei: MindElixirInstance) {
      mei.cancelFocus()
    },
  },
  {
    label: 'operation.moveUp',
    defaultLabel: 'Move Up',
    shortcut: 'Alt + ↑',
    hideIfRoot: true,
    onClick(mei: MindElixirInstance) {
      mei.moveUpNode()
    },
  },
  {
    label: 'operation.moveDown',
    defaultLabel: 'Move Down',
    shortcut: 'Alt + ↓',
    hideIfRoot: true,
    onClick(mei: MindElixirInstance) {
      mei.moveDownNode()
    },
  },
  {
    label: 'operation.link',
    defaultLabel: 'Link',
    onClick(mei: MindElixirInstance) {
      const from = mei.currentNode as Topic
      const tips = createTips(i18n.t('operation.linkTips'))
      mei.container.appendChild(tips)
      mei.map.addEventListener(
        'click',
        e => {
          e.preventDefault()
          tips.remove()
          const target = e.target as Topic
          if (target.parentElement.tagName === 'ME-PARENT' || target.parentElement.tagName === 'ME-ROOT') {
            mei.createArrow(from, target)
          } else {
            console.log('link cancel')
          }
        },
        {
          once: true,
        }
      )
    },
  },
  {
    label: 'operation.summary',
    defaultLabel: 'Summary',
    hideIfRoot: true,
    onClick(mei: MindElixirInstance) {
      mei.createSummary()
      mei.unselectNodes(mei.currentNodes)
    },
  },
]

export const otherShortcuts = [
  {
    label: 'shortcuts.displaySide',
    shortcut: 'Ctrl + ↑',
  },
  {
    label: 'shortcuts.displayLeft',
    shortcut: 'Ctrl + ←',
  },
  {
    label: 'shortcuts.displayRight',
    shortcut: 'Ctrl + →',
  },
  {
    label: 'shortcuts.save',
    shortcut: 'Ctrl + S',
  },
  {
    label: 'shortcuts.undo',
    shortcut: 'Ctrl + Z',
  },
  {
    label: 'shortcuts.copy',
    shortcut: 'Ctrl + C',
  },
  {
    label: 'shortcuts.paste',
    shortcut: 'Ctrl + V',
  },
  {
    label: 'shortcuts.cut',
    shortcut: 'Ctrl + X',
  },
  {
    label: 'shortcuts.moveToCenter',
    shortcut: 'F1',
  },
  {
    label: 'shortcuts.editTopic',
    shortcut: 'F2',
  },
  {
    label: 'shortcuts.zoomIn',
    shortcut: 'Ctrl + +',
  },
  {
    label: 'shortcuts.zoomOut',
    shortcut: 'Ctrl + -',
  },
  {
    label: 'shortcuts.resetZoom',
    shortcut: 'Ctrl + 0',
  },
  {
    label: 'shortcuts.toggleNodeMenu',
    shortcut: 'Ctrl + M',
  },
  {
    label: 'shortcuts.toggleOutlineMode',
    shortcut: 'Ctrl + O',
  },
  {
    label: 'shortcuts.showShortcutsDialog',
    shortcut: '?',
  },
]
