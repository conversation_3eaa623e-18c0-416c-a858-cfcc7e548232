const LeftIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_2_30)">
      <path d="M22 12H19.7037C19.0258 11.9999 18.3585 12.2737 17.7608 12.7971C17.1632 13.3204 16.6538 14.0771 16.2778 15C15.9018 15.9229 15.3924 16.6796 14.7947 17.2029C14.1971 17.7263 13.5297 18.0001 12.8519 18H12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M22 12L19.7037 12C19.0258 12.0001 18.3585 11.7263 17.7608 11.2029C17.1632 10.6796 16.6538 9.9229 16.2778 9C15.9018 8.0771 15.3924 7.3204 14.7947 6.79705C14.1971 6.27371 13.5297 5.99992 12.8519 6L12 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M3.66667 4H10.3333C11.2538 4 12 4.74619 12 5.66667V7.33333C12 8.25381 11.2538 9 10.3333 9H3.66667C2.74619 9 2 8.25381 2 7.33333V5.66667C2 4.74619 2.74619 4 3.66667 4Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M3.66667 15H10.3333C11.2538 15 12 15.7462 12 16.6667V18.3333C12 19.2538 11.2538 20 10.3333 20H3.66667C2.74619 20 2 19.2538 2 18.3333V16.6667C2 15.7462 2.74619 15 3.66667 15Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </g>
    <defs>
      <clipPath id="clip0_2_30">
        <rect width="24" height="24" fill="white" transform="matrix(-1 0 0 1 24 0)"/>
      </clipPath>
    </defs>
  </svg>
);

export default LeftIcon;
