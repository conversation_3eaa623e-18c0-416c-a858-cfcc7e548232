import { NodeObj, Topic } from 'mind-elixir'

export const isTopic = (target?: HTMLElement): target is Topic => {
  return target ? target.tagName === 'ME-TPC' : false
}

/**
 * 通用的节点搜索函数
 * @param node 要搜索的节点
 * @param keyword 搜索关键词
 * @param checkFn 检查函数，用于确定节点是否匹配关键词
 * @returns 匹配的结果或 undefined
 */
export const searchNode = <T>(
  node: NodeObj,
  keyword: string,
  checkFn: (node: NodeObj, keyword: string) => T | undefined
): T | undefined => {
  // 检查当前节点
  const result = checkFn(node, keyword)
  if (result !== undefined) {
    return result
  }

  // 递归检查子节点
  if (node.children) {
    for (let i = 0; i < node.children.length; i++) {
      const res = searchNode(node.children[i], keyword, checkFn)
      if (res !== undefined) {
        return res
      }
    }
  }

  return undefined
}

/**
 * 搜索节点主题
 */
export const searchTopic = (node: NodeObj, keyword: string): string | undefined => {
  return searchNode(node, keyword, (node, keyword) =>
    node.topic.includes(keyword) ? node.topic : undefined
  )
}

/**
 * 搜索节点标签
 */
export const searchTag = (node: NodeObj, keyword: string): string | undefined => {
  return searchNode(node, keyword, (node, keyword) =>
    node.tags && node.tags.includes(keyword) ? keyword : undefined
  )
}

export const searchId = (node: NodeObj, id: string): NodeObj | undefined => {
  return searchNode(node, id, (node, id) =>
    node.id === id ? node : undefined
  )
}

export const createTips = (words: string) => {
  const div = document.createElement('div')
  div.innerText = words
  div.className = 'tips'
  return div
}