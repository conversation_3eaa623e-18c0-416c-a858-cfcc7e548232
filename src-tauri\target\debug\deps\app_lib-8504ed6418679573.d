C:\git\mind-elixir-desktop\src-tauri\target\debug\deps\libapp_lib-8504ed6418679573.rmeta: src\lib.rs src\encrypt.rs src\mcp\mod.rs src\mcp\axum_router.rs src\mcp\common\mod.rs src\mcp\common\calculator.rs src\mcp\common\mindmap.rs C:\git\mind-elixir-desktop\src-tauri\target\debug\build\MindElixirDesktop-1711afbeadd4260b\out/6f7dea47d2eb9fc9b06f084017a2ecef066adf366acba1b73d229d9d70bd6dc1

C:\git\mind-elixir-desktop\src-tauri\target\debug\deps\app_lib-8504ed6418679573.d: src\lib.rs src\encrypt.rs src\mcp\mod.rs src\mcp\axum_router.rs src\mcp\common\mod.rs src\mcp\common\calculator.rs src\mcp\common\mindmap.rs C:\git\mind-elixir-desktop\src-tauri\target\debug\build\MindElixirDesktop-1711afbeadd4260b\out/6f7dea47d2eb9fc9b06f084017a2ecef066adf366acba1b73d229d9d70bd6dc1

src\lib.rs:
src\encrypt.rs:
src\mcp\mod.rs:
src\mcp\axum_router.rs:
src\mcp\common\mod.rs:
src\mcp\common\calculator.rs:
src\mcp\common\mindmap.rs:
C:\git\mind-elixir-desktop\src-tauri\target\debug\build\MindElixirDesktop-1711afbeadd4260b\out/6f7dea47d2eb9fc9b06f084017a2ecef066adf366acba1b73d229d9d70bd6dc1:

# env-dep:CARGO_PKG_AUTHORS=Time Record
# env-dep:CARGO_PKG_DESCRIPTION=Lightweight app, heavyweight thinking. Mind Elixir expands your creativity in just 10MB, keeps all data securely local, and enhances your thinking with AI while privacy remains in your hands.
# env-dep:CARGO_PKG_NAME=MindElixirDesktop
# env-dep:OUT_DIR=C:\\git\\mind-elixir-desktop\\src-tauri\\target\\debug\\build\\MindElixirDesktop-1711afbeadd4260b\\out
