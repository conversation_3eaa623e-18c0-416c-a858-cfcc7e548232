import { MindElixirInstance, NodeObj } from 'mind-elixir'
import { ScrollArea } from '../ui/scroll-area'
import { Outliner } from 'react-outliner-neo'
import 'react-outliner-neo/style.css'
import { useTranslation } from 'react-i18next'

export const OutlinerMode = ({
  mei,
  outlinerData,
  setOutlinerData,
  className,
  readonly,
}: {
  mei: MindElixirInstance | undefined
  outlinerData: NodeObj[]
  setOutlinerData: (data: NodeObj[]) => void
  className?: string
  readonly?: boolean
}) => {
  const { t } = useTranslation()
  return (
    <div className={'fixed top-0 left-0 w-full h-screen bg-background ' + className}>
      {mei?.nodeData && (
        <div className="max-w-[960px] mx-auto p-5 h-full">
          <div className=" text-2xl font-bold my-5 mx-6 text-right">{t('mindmap.outlineMode')}</div>
          <ScrollArea style={{
            height: 'calc(100vh - 106px)',
          }}>
            <h1 className="mx-6 text-2xl">{mei!.nodeData.topic}</h1>
            <Outliner
              readonly={readonly}
              data={outlinerData}
              onChange={data => {
                setOutlinerData(data)
              }}
            />
          </ScrollArea>
        </div>
      )}
    </div>
  )
}
