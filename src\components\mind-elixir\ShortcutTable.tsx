import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { operations, otherShortcuts } from '@/utils/operationMap'
import { useTranslation } from 'react-i18next'

const shortcuts = operations.filter(item => item.shortcut)
const ShortcutTable = () => {
  const { t } = useTranslation()
  return (
    <Table>
      {/* <TableCaption>A list of your recent invoices.</TableCaption> */}
      <TableHeader>
        <TableRow>
          <TableHead>{t('shortcuts.action')}</TableHead>
          <TableHead className="text-right">{t('shortcuts.shortcut')}</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {[...shortcuts, ...otherShortcuts].map(shortcut => (
          <TableRow key={shortcut.label}>
            <TableCell className="font-medium">{t(shortcut.label)}</TableCell>
            <TableCell className="text-right">{shortcut.shortcut}</TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}

export default ShortcutTable
