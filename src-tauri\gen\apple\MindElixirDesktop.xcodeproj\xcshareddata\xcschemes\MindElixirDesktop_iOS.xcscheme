<?xml version="1.0" encoding="UTF-8"?>
<Scheme
   LastUpgradeVersion = "1430"
   version = "1.3">
   <BuildAction
      parallelizeBuildables = "YES"
      buildImplicitDependencies = "YES">
      <BuildActionEntries>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "YES"
            buildForProfiling = "YES"
            buildForArchiving = "YES"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "F84F23A68FB9387C66A4FAC1"
               BuildableName = "Mind Elixir.app"
               BlueprintName = "MindElixirDesktop_iOS"
               ReferencedContainer = "container:MindElixirDesktop.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
      </BuildActionEntries>
   </BuildAction>
   <TestAction
      buildConfiguration = "debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      shouldUseLaunchSchemeArgsEnv = "NO">
      <MacroExpansion>
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "F84F23A68FB9387C66A4FAC1"
            BuildableName = "Mind Elixir.app"
            BlueprintName = "MindElixirDesktop_iOS"
            ReferencedContainer = "container:MindElixirDesktop.xcodeproj">
         </BuildableReference>
      </MacroExpansion>
      <EnvironmentVariables>
         <EnvironmentVariable
            key = "RUST_BACKTRACE"
            value = "full"
            isEnabled = "YES">
         </EnvironmentVariable>
         <EnvironmentVariable
            key = "RUST_LOG"
            value = "info"
            isEnabled = "YES">
         </EnvironmentVariable>
      </EnvironmentVariables>
      <Testables>
      </Testables>
   </TestAction>
   <LaunchAction
      buildConfiguration = "debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      launchStyle = "0"
      useCustomWorkingDirectory = "NO"
      ignoresPersistentStateOnLaunch = "NO"
      debugDocumentVersioning = "YES"
      debugServiceExtension = "internal"
      allowLocationSimulation = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "F84F23A68FB9387C66A4FAC1"
            BuildableName = "Mind Elixir.app"
            BlueprintName = "MindElixirDesktop_iOS"
            ReferencedContainer = "container:MindElixirDesktop.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
      <EnvironmentVariables>
         <EnvironmentVariable
            key = "RUST_BACKTRACE"
            value = "full"
            isEnabled = "YES">
         </EnvironmentVariable>
         <EnvironmentVariable
            key = "RUST_LOG"
            value = "info"
            isEnabled = "YES">
         </EnvironmentVariable>
      </EnvironmentVariables>
   </LaunchAction>
   <ProfileAction
      buildConfiguration = "release"
      shouldUseLaunchSchemeArgsEnv = "NO"
      savedToolIdentifier = ""
      useCustomWorkingDirectory = "NO"
      debugDocumentVersioning = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "F84F23A68FB9387C66A4FAC1"
            BuildableName = "Mind Elixir.app"
            BlueprintName = "MindElixirDesktop_iOS"
            ReferencedContainer = "container:MindElixirDesktop.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
      <EnvironmentVariables>
         <EnvironmentVariable
            key = "RUST_BACKTRACE"
            value = "full"
            isEnabled = "YES">
         </EnvironmentVariable>
         <EnvironmentVariable
            key = "RUST_LOG"
            value = "info"
            isEnabled = "YES">
         </EnvironmentVariable>
      </EnvironmentVariables>
   </ProfileAction>
   <AnalyzeAction
      buildConfiguration = "debug">
   </AnalyzeAction>
   <ArchiveAction
      buildConfiguration = "release"
      revealArchiveInOrganizer = "YES">
   </ArchiveAction>
</Scheme>
