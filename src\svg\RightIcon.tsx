const RightIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_2_57)">
      <path
        d="M2 12H4.29633C4.97417 11.9999 5.64153 12.2737 6.23916 12.7971C6.8368 13.3204 7.34623 14.0771 7.72222 15C8.09822 15.9229 8.60764 16.6796 9.20528 17.2029C9.80292 17.7263 10.4703 18.0001 11.1481 18H12"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2 12L4.29633 12C4.97417 12.0001 5.64153 11.7263 6.23916 11.2029C6.8368 10.6796 7.34623 9.9229 7.72222 9C8.09822 8.0771 8.60764 7.3204 9.20528 6.79705C9.80292 6.27371 10.4703 5.99992 11.1481 6L12 6"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M20.3333 4H13.6667C12.7462 4 12 4.74619 12 5.66667V7.33333C12 8.25381 12.7462 9 13.6667 9H20.3333C21.2538 9 22 8.25381 22 7.33333V5.66667C22 4.74619 21.2538 4 20.3333 4Z"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M20.3333 15H13.6667C12.7462 15 12 15.7462 12 16.6667V18.3333C12 19.2538 12.7462 20 13.6667 20H20.3333C21.2538 20 22 19.2538 22 18.3333V16.6667C22 15.7462 21.2538 15 20.3333 15Z"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_2_57">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
)

export default RightIcon
