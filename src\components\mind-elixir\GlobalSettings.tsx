import { useCallback, useEffect } from 'react'
import { useMindElixirStore } from '@/store'
import { useTranslation } from 'react-i18next'
import { Input } from '../ui/input'
import { FormLabel, FormControl, FormField, FormItem, Form } from '../ui/form'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { ScrollArea } from '../ui/scroll-area'
import { themeMap } from '@/theme'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'

const formSchema = z.object({
  theme: z.string().optional(),
  source: z.string().optional(),
})

const GlobalSettings = () => {
  const { t } = useTranslation()
  const mei = useMindElixirStore(state => state.mei)
  const file = useMindElixirStore(state => state.file)

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zod<PERSON>esolver(formSchema),
  })

  useEffect(() => {
    if (mei?.theme.name) {
      form.setValue('theme', mei.theme.name)
    }
    if (file?.source !== undefined) {
      form.setValue('source', file.source)
    }
  }, [mei?.theme.name, file?.source, form])

  const handleThemeChange = useCallback(
    (theme: string) => {
      if (mei && theme) {
        mei.changeTheme(themeMap[theme])
      }
    },
    [mei]
  )

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString()
  }

  if (!file) return null

  return (
    <ScrollArea className="h-full overflow-auto">
      <div className="p-3 pt-1">
        <Form {...form}>
          <form className="space-y-5">
            {/* 主题选择 */}
            <FormField
              control={form.control}
              name="theme"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('nodeMenu.theme')}</FormLabel>
                  <FormControl>
                    <Select
                      value={field.value}
                      onValueChange={value => {
                        field.onChange(value)
                        handleThemeChange(value)
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择主题" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.keys(themeMap).map(key => (
                          <SelectItem key={key} value={key}>
                            {key}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                </FormItem>
              )}
            />

            {/* 数据源设置 */}
            <FormField
              control={form.control}
              name="source"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('nodeMenu.source')}</FormLabel>
                  <div className="flex gap-2">
                    <FormControl>
                      <Input
                        placeholder={t('nodeMenu.enterSource')}
                        value={field.value || ''}
                        onChange={e => {
                          field.onChange(e.target.value)
                        }}
                      />
                    </FormControl>
                  </div>
                </FormItem>
              )}
            />

            {/* 文件信息 */}
            <FormItem>
              <FormLabel>{t('nodeMenu.fileInfo')}</FormLabel>
              <div className="space-y-2 text-sm text-muted-foreground">
                <div className="flex justify-between">
                  <span>{t('nodeMenu.createdAt')}:</span>
                  <span>{formatDate(file.createdAt)}</span>
                </div>
                <div className="flex justify-between">
                  <span>{t('nodeMenu.updatedAt')}:</span>
                  <span>{formatDate(file.updatedAt)}</span>
                </div>
              </div>
            </FormItem>
          </form>
        </Form>
      </div>
    </ScrollArea>
  )
}

export default GlobalSettings
