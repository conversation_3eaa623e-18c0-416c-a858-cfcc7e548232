import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { BrainCog, Globe, Palette, User, Waypoints, FolderOpen, Info } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import { Theme, useTheme } from '@/components/theme-provider'
import { useUserInfoStore, useUserSettings } from '@/store'
import { Input, PasswordInput } from '../ui/input'
import { ModelSelector } from '../model-selector'
import { toast } from 'sonner'
import { openUrl, openPath } from '@tauri-apps/plugin-opener'
import { appDataDir } from '@tauri-apps/api/path'
import { getVersion } from '@tauri-apps/api/app'
import { check, reCheck } from '@/utils/check'
import { useEffect, useState } from 'react'
import UserProfileAvatar, { UserType } from './UserProfileAvatar'
import { MemberBadge } from './MemberBadge'

const loginGithub = () => {
  openUrl('https://mind-elixir-backend.fly.dev/oauth/github/login?port=6595&type=desktop')
}
const loginGoogle = () => {
  openUrl('https://mind-elixir-backend.fly.dev/oauth/google/login?port=6595&type=desktop')
}

export default function UserSettings() {
  const { t, i18n } = useTranslation()

  const openDataFolder = async () => {
    try {
      const dataDir = await appDataDir()
      await openPath(dataDir)
      toast.success(t('settings.storage.openDataFolderDescription'))
    } catch (error) {
      console.error('Failed to open data folder:', error)
      toast.error(t('error.general'))
    }
  }
  const { theme, setTheme } = useTheme()
  const {
    language,
    proxy,
    proxyEnabled,
    geminiKey,
    openaiKey,
    openaiModel,
    geminiModel,
    openaiCompatibleModel,
    aiProvider,
    aiMode,
    setAiMode,
    setLanguage,
    setProxy,
    setProxyEnabled,
    setGeminiKey,
    setOpenaiKey,
    setOpenaiModel,
    setGeminiModel,
    setOpenaiCompatibleModel,
    setAiProvider,
    baseUrl,
    setBaseUrl,
  } = useUserSettings()
  const { userInfo } = useUserInfoStore()
  const handleLanguageChange = (value: string) => {
    setLanguage(value)
    i18n.changeLanguage(value)
  }

  const [isPremium, setIsPremium] = useState(false)
  const [version, setVersion] = useState<string>('')

  useEffect(() => {
    check().then(res => {
      setIsPremium(res)
    })

    // Get app version
    getVersion().then(ver => {
      setVersion(ver)
    }).catch(err => {
      console.error('Failed to get app version:', err)
    })
  }, [])
  const logout = () => {
    // Clear user info by setting it to empty values
    const emptyUserInfo = {
      _id: '',
      providerAccountId: '',
      provider: '',
      email: '',
      image: '',
      name: '',
      uuid: '',
      type: '',
    }
    useUserInfoStore.getState().setUserInfo(emptyUserInfo)
    setIsPremium(false)
    localStorage.setItem('token', '')
    localStorage.setItem('dd', '')
    toast.success(t('auth.logoutSuccess'))
  }

  const handleThemeChange = (value: string) => {
    setTheme(value as Theme)
  }

  return (
    <div className="flex flex-col h-full w-full max-w-4xl m-auto bg-background overflow-auto space-y-6 sm:space-y-8 p-4 sm:p-8 pt-4 sm:pt-6">
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-2xl sm:text-3xl font-bold tracking-tight">{t('settings.title')}</h2>
      </div>

      <div className="space-y-6">
        <div className="space-y-4">
          <div className="flex items-center space-x-4">
            <User className="h-5 w-5" />
            <h3 className="text-lg font-medium">{t('settings.account.title')}</h3>
          </div>
          <div className="bg-card rounded-lg p-6 shadow-sm border border-border">
            <div className="flex flex-col sm:flex-row items-center gap-6">
              <div className="relative">
                <UserProfileAvatar size="lg" image={userInfo?.image} name={userInfo?.name} type={userInfo?.type as UserType} />
              </div>
              <div className="space-y-4 flex-1 text-center sm:text-left">
                <div>
                  <div className="text-xs uppercase text-muted-foreground tracking-wide mb-1">{t('common.username')}</div>
                  <div className="text-lg font-medium">
                    {userInfo?.name || '--'}
                    {userInfo?.type && isPremium && <MemberBadge className="ml-3" type={userInfo.type as 'annual' | 'lifetime'} />}
                  </div>
                </div>
                <div>
                  <div className="text-xs uppercase text-muted-foreground tracking-wide mb-1">{t('common.email')}</div>
                  <div className="text-sm">{userInfo?.email || '--'}</div>
                </div>
              </div>
            </div>
            <div className="mt-3 flex flex-col sm:flex-row justify-end gap-2 sm:gap-3">
              {userInfo?.name && !isPremium && (
                <>
                  <Button
                    size="sm"
                    className="w-full sm:w-auto"
                    onClick={() => {
                      openUrl('https://desktop.mind-elixir.com/#pricing')
                    }}
                  >
                    {t('settings.account.upgradeToPro')}
                  </Button>
                  <Button
                    size="sm"
                    className="w-full sm:w-auto"
                    onClick={() => {
                      reCheck().then(res => {
                        setIsPremium(res)
                      })
                    }}
                  >
                    {t('settings.account.checkLicense')}
                  </Button>
                </>
              )}
              {userInfo?.name ? (
                <Button variant="destructive" size="sm" className="w-full sm:w-auto px-6" onClick={logout}>
                  {t('common.logout')}
                </Button>
              ) : (
                <>
                  <Button variant="default" size="sm" className="w-full sm:w-auto px-6" onClick={loginGithub}>
                    {t('settings.account.githubLogin')}
                  </Button>
                  <Button variant="default" size="sm" className="w-full sm:w-auto px-6" onClick={loginGoogle}>
                    {t('settings.account.googleLogin')}
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center space-x-4">
            <Palette className="h-5 w-5" />
            <h3 className="text-lg font-medium">{t('settings.appearance.title')}</h3>
          </div>
          <Separator />
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>{t('settings.appearance.theme')}</Label>
              <Select value={theme} onValueChange={handleThemeChange}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="light">{t('settings.appearance.light')}</SelectItem>
                  <SelectItem value="dark">{t('settings.appearance.dark')}</SelectItem>
                  <SelectItem value="system">{t('settings.appearance.system')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center space-x-4">
            <BrainCog className="h-5 w-5" />
            <h3 className="text-lg font-medium">{t('settings.ai.title')}</h3>
          </div>
          <Separator />
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>{t('settings.ai.mode')}</Label>
              <Select
                value={aiMode}
                onValueChange={(value: string) => {
                  setAiMode(value as 'normal' | 'context-aware')
                }}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="normal">{t('settings.ai.normal')}</SelectItem>
                  <SelectItem value="context-aware">{t('settings.ai.contextAware')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>{t('settings.ai.aiProvider')}</Label>
              <Select
                value={aiProvider}
                onValueChange={(value: string) => {
                  setAiProvider(value as 'gemini' | 'openai' | 'openai-compatible')
                }}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="gemini">{t('settings.ai.gemini')}</SelectItem>
                  <SelectItem value="openai">{t('settings.ai.openai')}</SelectItem>
                  <SelectItem value="openai-compatible">{t('settings.ai.openaiCompatible')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {aiProvider === 'gemini' && (
              <>
                <div className="space-y-2">
                  <Label>{t('settings.ai.geminiKey')}</Label>
                  <PasswordInput value={geminiKey} onChange={e => setGeminiKey(e.target.value)} />
                </div>
                <div className="space-y-2">
                  <Label>{t('settings.ai.geminiModel')}</Label>
                  <ModelSelector
                    baseUrl="https://generativelanguage.googleapis.com/v1beta/openai"
                    apiKey={geminiKey}
                    value={geminiModel}
                    onChange={(model: string) => {
                      setGeminiModel(model)
                    }}
                  />
                </div>
              </>
            )}
            {aiProvider === 'openai-compatible' && (
              <>
                <div className="space-y-2">
                  <Label>{t('settings.ai.baseUrl')}</Label>
                  <Input value={baseUrl} onChange={e => setBaseUrl(e.target.value)} />
                </div>
                <div className="space-y-2">
                  <Label>{t('settings.ai.openaiKey')}</Label>
                  <PasswordInput value={openaiKey} onChange={e => setOpenaiKey(e.target.value)} />
                </div>
                <div className="space-y-2">
                  <Label>{t('settings.ai.openaiModel')}</Label>
                  <ModelSelector
                    baseUrl={baseUrl}
                    apiKey={openaiKey}
                    value={openaiCompatibleModel}
                    onChange={(model: string) => {
                      setOpenaiCompatibleModel(model)
                    }}
                  />
                </div>
              </>
            )}
            {aiProvider === 'openai' && (
              <>
                <div className="space-y-2">
                  <Label>{t('settings.ai.openaiKey')}</Label>
                  <PasswordInput value={openaiKey} onChange={e => setOpenaiKey(e.target.value)} />
                </div>
                <div className="space-y-2">
                  <Label>{t('settings.ai.openaiModel')}</Label>
                  <ModelSelector
                    baseUrl="https://api.openai.com/v1"
                    apiKey={openaiKey}
                    value={openaiModel}
                    onChange={(model: string) => {
                      setOpenaiModel(model)
                    }}
                  />
                </div>
              </>
            )}
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center space-x-4">
            <Waypoints className="h-5 w-5" />
            <h3 className="text-lg font-medium">{t('settings.proxy.title')}</h3>
          </div>
          <Separator />
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>{t('settings.proxy.enable')}</Label>
              </div>
              <Switch checked={proxyEnabled} onCheckedChange={checked => setProxyEnabled(checked)} />
            </div>
            <div className="space-y-2">
              <Label>{t('settings.proxy.url')}</Label>
              <Input value={proxy} onChange={e => setProxy(e.target.value)} />
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center space-x-4">
            <Globe className="h-5 w-5" />
            <h3 className="text-lg font-medium">{t('settings.language.title')}</h3>
          </div>
          <Separator />
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>{t('settings.language.select')}</Label>
              <Select value={language} onValueChange={handleLanguageChange}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="en">English</SelectItem>
                  <SelectItem value="zh">中文</SelectItem>
                  <SelectItem value="zh-TW">繁體中文</SelectItem>
                  <SelectItem value="ja">日本語</SelectItem>
                  <SelectItem value="ko">한국어</SelectItem>
                  <SelectItem value="de">Deutsch</SelectItem>
                  <SelectItem value="es">Español</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center space-x-4">
            <FolderOpen className="h-5 w-5" />
            <h3 className="text-lg font-medium">{t('settings.storage.title')}</h3>
          </div>
          <Separator />
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>{t('settings.storage.openDataFolder')}</Label>
              <p className="text-sm text-muted-foreground">{t('settings.storage.openDataFolderDescription')}</p>
              <Button variant="outline" onClick={openDataFolder} className="w-fit">
                <FolderOpen className="h-4 w-4 mr-2" />
                {t('settings.storage.openDataFolder')}
              </Button>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center space-x-4">
            <Info className="h-5 w-5" />
            <h3 className="text-lg font-medium">{t('settings.about.title')}</h3>
          </div>
          <Separator />
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>{t('settings.about.version')}</Label>
              <p className="text-sm text-muted-foreground">{version || 'Loading...'}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
