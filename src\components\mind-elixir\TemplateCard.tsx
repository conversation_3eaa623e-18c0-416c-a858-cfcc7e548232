import { deleteMap, MindElixirLocal, saveMap } from '@/api/map'
import { useLocation } from 'wouter'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '../ui/dropdown-menu'
import { Button } from '../ui/button'
import { MoreHorizontal } from 'lucide-react'
import { useEffect, useRef } from 'react'
import MindElixir from 'mind-elixir'
import { generateFileId } from '@/utils/common'

const TemplateCard = ({ type, map, onDone }: { type: 'template' | 'share'; map: MindElixirLocal; onDone: () => void }) => {
  const [, navigate] = useLocation()
  const divRef = useRef<HTMLDivElement>(null)
  const removeMap = async (id: string) => {
    await deleteMap(id)
    onDone()
  }
  useEffect(() => {
    if (!divRef.current) return
    const mei = new MindElixir({
      el: divRef.current,
      editable: false,
      draggable: false,
      toolBar: false,
      contextMenu: false,
    })
    const copy = JSON.parse(JSON.stringify(map.content))
    mei.init(copy)
    // mei.scale(0.2)
    const handleResize = () => {
      mei.toCenter()
    }
    window.addEventListener('resize', handleResize)
    return () => {
      mei.destroy()
      window.removeEventListener('resize', handleResize)
    }
  }, [map])
  return (
    <div className="border rounded-md overflow-hidden bg-background group">
      <div
        className="relative aspect-square bg-muted cursor-pointer"
        onClick={() => {
          const id = generateFileId()
          if (type === 'share') navigate('/share?id=' + map.id)
          else navigate('/edit?id=' + id + '&template=' + map.id)
        }}
      >
        <div className="aspect-square pointer-events-none" ref={divRef}>
          Preview
        </div>
        <div className="absolute top-1 right-1">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-6 w-6" onClick={e => e.stopPropagation()}>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={async e => {
                  e.stopPropagation()
                  const id = generateFileId()
                  await saveMap(id, {
                    content: map.content,
                    id,
                    createdAt: +Date.now(),
                    updatedAt: +Date.now(),
                  })
                  navigate('/edit?id=' + id)
                }}
              >
                Save as New Map
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <div className="p-2">
        <h3 className="font-medium truncate">{map.content.nodeData.topic}</h3>
        {/* <p className="text-xs text-muted-foreground">Last edited: {map.updatedAt}</p> */}
      </div>
    </div>
  )
}

export default TemplateCard
