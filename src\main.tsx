import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import { dirInit } from './api/map.ts'
import { getCurrentWindow } from '@tauri-apps/api/window'
import { i18nInit } from './i18n/config'
import './index.css'

const window = getCurrentWindow()
console.log(window)
async function init() {
  await dirInit()
  await i18nInit()
  document.addEventListener('contextmenu', event => event.preventDefault())
  document.addEventListener('dragstart', event => {
    const target = event.target as HTMLElement
    if (target.tagName === 'A') {
      event.preventDefault()
    }
  })
  document.addEventListener('keydown', async event => {
    console.log(event.key)
    if (event.key === 'Escape') { 
      await window.setFullscreen(false)
    }
  })
  createRoot(document.getElementById('root')!).render(
    <StrictMode>
      <App />
    </StrictMode>
  )
}

init()
