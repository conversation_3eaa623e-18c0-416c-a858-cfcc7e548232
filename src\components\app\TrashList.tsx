import { Trash } from 'lucide-react'
import { Button } from '../ui/button'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { listTrash, clearTrash } from '@/api/map'
import { DeleteAlertDialog } from './DeleteAlertDialog'
import BaseListView, { BaseListViewConfig } from './BaseListView'

const TrashList = () => {
  const { t } = useTranslation()
  const [dialogOpen, setDialogOpen] = useState(false)

  const handleClick = async () => {
    await clearTrash()
    // 清空后需要刷新列表，这里可以通过重新渲染组件来实现
    window.location.reload()
  }

  const config: BaseListViewConfig = {
    listApi: listTrash,
    searchDir: 'trash',
    cardType: 'trash',
    mobileHeightOffset: 160,
    desktopHeightOffset: 56,
    mobileMinHeightOffset: 240,
    desktopMinHeightOffset: 140,
    actionButtons: (
      <Button size="sm" className="h-8 w-full sm:w-auto" onClick={() => setDialogOpen(true)}>
        <Trash className="h-4 w-4 sm:mr-1" />
        <span className="hidden sm:inline">{t('common.emptyRecycleBin')}</span>
      </Button>
    ),
    dialogs: (
      <DeleteAlertDialog open={dialogOpen} setOpen={setDialogOpen} onConfirm={handleClick} />
    ),
    emptyStateContent: null
  }

  return <BaseListView config={config} />
}

export default TrashList
