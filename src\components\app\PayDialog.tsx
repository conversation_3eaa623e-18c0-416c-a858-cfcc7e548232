import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { useUserInfoStore } from '@/store'
import { openUrl } from '@tauri-apps/plugin-opener'
import { useTranslation } from 'react-i18next'
import { navigate } from 'wouter/use-browser-location';

export function PayDialog({ open, setOpen }: { open: boolean; setOpen: (open: boolean) => void }) {
  const { t } = useTranslation()
  const { userInfo } = useUserInfoStore()
  return (
    <AlertDialog open={open} onOpenChange={setOpen}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{t('dialog.limitReached')}</AlertDialogTitle>
          <AlertDialogDescription>{t('dialog.subscriptionRequired')}</AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>{t('dialog.maybeLater')}</AlertDialogCancel>
          {userInfo?.name ? (
            <AlertDialogAction
              onClick={() => {
                openUrl('https://desktop.mind-elixir.com/#pricing')
              }}
            >
              {t('dialog.subscribeNow')}
            </AlertDialogAction>
          ) : (
            <AlertDialogAction
              onClick={() => {
                navigate('/settings')
              }}
            >
              {t('settings.account.pleaseLogin')}
            </AlertDialogAction>
          )}
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
