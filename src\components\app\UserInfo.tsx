import { useUserInfoStore } from '@/store'
import { navigate } from 'wouter/use-browser-location'
import { useTranslation } from 'react-i18next'
import UserProfileAvatar, { UserType } from './UserProfileAvatar'

const UserInfo = () => {
  const { userInfo } = useUserInfoStore()
  const { t } = useTranslation()
  const login = () => {
    navigate('/settings')
  }

  return (
    <div className="mt-auto cursor-pointer" onClick={login}>
      {userInfo && userInfo.name ? (
        <div className="flex items-center gap-2 rounded-md px-2 py-1">
          <UserProfileAvatar size="sm" image={userInfo.image} name={userInfo.name} type={userInfo.type as UserType} />
          <div className="text-xs truncate">{userInfo.name}</div>
        </div>
      ) : (
        <div>{t('settings.account.pleaseLogin')}</div>
      )}
    </div>
  )
}

export default UserInfo
