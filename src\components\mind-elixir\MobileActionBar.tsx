import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { useMindElixirStore } from '@/store'
import { useTranslation } from 'react-i18next'
import { operations } from '@/utils/operationMap'
import { aiReqStream, MindMapTransformStream } from '@/api/ai'
import { convertToMd } from '@/utils/markdown'
import { useUserSettings } from '@/store'
import { toast } from 'sonner'
import i18n from '@/i18n/config'
import MindElixir, { MindElixirInstance, NodeObj } from 'mind-elixir'
import {
  Plus,
  Trash2,
  Brain,
  MoreHorizontal,
  X,
  Send
} from 'lucide-react'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet'

const aiChildren = async (node: NodeObj, mei: MindElixirInstance, prompt?: string) => {
  const loader = document.createElement('div')
  loader.className = 'loader'
  const el = MindElixir.E(node.id)
  el.appendChild(loader)

  const thinking = document.createElement('me-children')
  thinking.className = 'thinking'
  thinking.innerText = 'Thinking...'

  const setting = useUserSettings.getState()
  try {
    const context = setting.aiMode === 'context-aware' ? convertToMd(mei.getData().nodeData, node) : ''
    const stream = aiReqStream(node.topic, prompt, context)
    const transformer = new MindMapTransformStream()
    const reader = stream.pipeThrough(transformer).getReader()
    while (true) {
      const { done, value } = await reader.read()
      if (done) {
        break
      }
      if (value.type === 'thinkingStart') {
        node.note = node.note || ''
        if (node.children?.length) {
          el.parentElement.nextSibling.appendChild(thinking)
        } else {
          el.parentElement.parentElement.appendChild(thinking)
        }
        try {
          mei.linkDiv()
        } catch (e) {
          console.log(e)
        }
      } else if (value.type === 'thinkingEnd') {
        thinking.remove()
        mei.linkDiv()
      } else if (value.type === 'thinking') {
        node.note += value.payload.content
        thinking.innerText = node.note || ''
        thinking.scrollTop = 99999
      } else if (value.type === 'addChild') {
        mei.addChild(el, {
          topic: value.payload.topic || '',
          id: value.payload.id,
        })
      } else if (value.type === 'editTopic') {
        mei.setNodeTopic(MindElixir.E(value.payload.id), value.payload.topic || '')
      }
    }
    mei!.refresh()
  } catch (e: unknown) {
    console.trace(e)
    loader.remove()
    const err = JSON.stringify(e)
    toast.error(err + ' ' + i18n.t('settings.ai.checkSettings'))
  }
}

const MobileActionBar = () => {
  const mei = useMindElixirStore(state => state.mei)
  const node = useMindElixirStore(state => state.currentNode)
  const nodes = useMindElixirStore(state => state.currentNodes)
  const [prompt, setPrompt] = useState('')
  const [isAiSheetOpen, setIsAiSheetOpen] = useState(false)
  const [isMoreSheetOpen, setIsMoreSheetOpen] = useState(false)
  const { t } = useTranslation()

  // 如果没有选中节点，不显示操作栏
  if (!nodes.length || !mei) {
    return null
  }

  const isRoot = !node?.parent
  const isSingleNode = nodes.length === 1

  const handleAiGenerate = async () => {
    if (!node || !mei) return
    
    setIsAiSheetOpen(false)
    await aiChildren(node, mei, prompt)
    setPrompt('')
  }

  const quickActions = [
    {
      icon: Plus,
      label: t('operation.addChild'),
      onClick: () => mei.addChild(),
      show: isSingleNode
    },
    {
      icon: Plus,
      label: t('operation.addSibling'),
      onClick: () => mei.insertSibling('after'),
      show: isSingleNode && !isRoot
    },
    {
      icon: Trash2,
      label: t('operation.remove'),
      onClick: () => mei.removeNodes(mei.currentNodes),
      show: !isRoot,
      variant: 'destructive' as const
    },
    {
      icon: Brain,
      label: t('ai.aiChildren'),
      onClick: () => setIsAiSheetOpen(true),
      show: isSingleNode
    }
  ]

  const moreOperations = operations.filter(item => {
    return !(isRoot && item.hideIfRoot)
  })

  return (
    <>
      {/* 移动端操作栏 */}
      <div className="fixed bottom-4 left-4 right-4 z-50 bg-background border rounded-lg shadow-lg p-3">
        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center gap-2 flex-1 overflow-x-auto">
            {quickActions
              .filter(action => action.show)
              .map((action, index) => (
                <Button
                  key={index}
                  variant={action.variant || 'outline'}
                  size="sm"
                  onClick={action.onClick}
                  className="flex-shrink-0"
                >
                  <action.icon className="h-4 w-4 mr-1" />
                  <span className="text-xs">{action.label}</span>
                </Button>
              ))}
          </div>
          
          {/* 更多操作按钮 */}
          <Sheet open={isMoreSheetOpen} onOpenChange={setIsMoreSheetOpen}>
            <SheetTrigger asChild>
              <Button variant="outline" size="sm" className="flex-shrink-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </SheetTrigger>
            <SheetContent side="bottom" className="h-[60vh]">
              <SheetHeader>
                <SheetTitle>{t('operation.moreActions')}</SheetTitle>
                <SheetDescription>
                  {nodes.length === 1 ? t('operation.singleNodeSelected') : t('operation.multipleNodesSelected', { count: nodes.length })}
                </SheetDescription>
              </SheetHeader>
              <div className="grid grid-cols-2 gap-2 mt-4">
                {moreOperations.map((operation, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    className="h-12 flex flex-col gap-1"
                    onClick={() => {
                      setIsMoreSheetOpen(false)
                      setTimeout(() => {
                        operation.onClick(mei)
                      }, 200)
                    }}
                  >
                    <span className="text-sm">{t(operation.label)}</span>
                    {operation.shortcut && (
                      <span className="text-xs text-muted-foreground">{operation.shortcut}</span>
                    )}
                  </Button>
                ))}
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>

      {/* AI 生成子节点的 Sheet */}
      <Sheet open={isAiSheetOpen} onOpenChange={setIsAiSheetOpen}>
        <SheetContent side="bottom" className="h-[50vh]">
          <SheetHeader>
            <SheetTitle>{t('ai.aiChildren')}</SheetTitle>
            <SheetDescription>
              {t('ai.aiChildrenDescription')}
            </SheetDescription>
          </SheetHeader>
          <div className="mt-4 space-y-4">
            <Textarea
              placeholder={t('ai.promptPlaceholder')}
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              className="min-h-[100px]"
            />
            <div className="flex gap-2">
              <Button
                onClick={handleAiGenerate}
                className="flex-1"
                disabled={!prompt.trim()}
              >
                <Send className="h-4 w-4 mr-2" />
                {t('ai.generate')}
              </Button>
              <Button
                variant="outline"
                onClick={() => setIsAiSheetOpen(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </>
  )
}

export default MobileActionBar
