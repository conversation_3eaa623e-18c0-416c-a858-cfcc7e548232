import { useUserSettings } from '@/store'
import { fetch } from '@tauri-apps/plugin-http'
import { toast } from 'sonner'
import { navigate } from 'wouter/use-browser-location'
import i18n from '@/i18n/config'

export const req = async (input: RequestInfo | URL, init?: RequestInit) => {
  const setting = useUserSettings.getState()
  const proxy = setting.proxy
  const proxyEnabled = setting.proxyEnabled
  const baseUrl = 'https://mind-elixir-backend.fly.dev'
  // const baseUrl = 'http://localhost:7001'
  if (typeof input === 'string') {
    input = baseUrl + input
  }
  const headers = {
    'Content-Type': 'application/json',
    Cookie: `mindelixir=${localStorage.getItem('token')}`,
    Origin: 'https://mind-elixir-backend.fly.dev',
    // Origin: '',
    ...init?.headers,
  }

  const response = await fetch(input, {
    proxy: proxyEnabled
      ? {
        all: proxy,
      }
      : undefined,
    credentials: 'include',
    headers,
    ...init,
  })

  // 检查401错误
  if (response.status === 401) {
    // 显示登录提醒
    toast.error(i18n.t('error.unauthorized'), {
      action: {
        label: i18n.t('settings.account.pleaseLogin'),
        onClick: () => navigate('/settings')
      }
    })
  }

  return response
}
