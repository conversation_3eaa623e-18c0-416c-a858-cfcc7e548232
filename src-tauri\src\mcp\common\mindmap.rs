use std::{future::Future, sync::Arc};

use rmcp::{
    model::{ServerCapabilities, ServerInfo},
    schemars::{self},
    tool, ServerHandler,
};
use serde::Serialize;
use tauri::{AppHandle, Emitter, Event, Listener};
use tokio::sync::oneshot;

pub fn once_ready(app: Arc<AppHandle>) -> impl Future<Output = Event> {
    let (tx, rx) = oneshot::channel();

    app.once("success", move |event| {
        let _ = tx.send(event.clone());
    });

    async move { rx.await.expect("failed to receive ready event") }
}

#[derive(Clone, Serialize)]
#[serde(rename_all = "camelCase", tag = "event", content = "data")]
enum MindmapEvent<'a> {
    #[serde(rename_all = "camelCase")]
    GetAllNodes {},
    #[serde(rename_all = "camelCase")]
    GenerateMindmap { mindmap_data: &'a str },
    #[serde(rename_all = "camelCase")]
    AddChild { parent_id: &'a str, topic: &'a str },
    #[serde(rename_all = "camelCase")]
    EditTopic { node_id: &'a str, topic: &'a str },
    #[serde(rename_all = "camelCase")]
    CreateSummary {
        text: &'a str,
        parent: &'a str,
        start: i8,
        end: i8,
    },
    CreateArrow {
        label: &'a str,
        from: &'a str,
        to: &'a str,
        bidirectional: bool,
    },
}

#[derive(Debug, Clone)]
pub struct Mindmap {
    app: Arc<AppHandle>,
}
#[tool(tool_box)]
impl Mindmap {
    pub fn new(handle: AppHandle) -> Self {
        Self {
            app: Arc::new(handle),
        }
    }
    #[tool(description = "Get All Mindmap Nodes")]
    async fn get_all_nodes(&self) -> String {
        let _ = self.app.emit("operation", MindmapEvent::GetAllNodes {});
        let e = once_ready(self.app.clone()).await;
        e.payload().to_string()
    }
    #[tool(description = "Generate a whole new mindmap")]
    async fn generate_mindmap(
        &self,
        #[tool(param)]
        #[schemars(description = "
```ts
export interface NodeObj {
  topic: string;
  id: Uid;
  children?: NodeObj[];
}

export interface Summary {
  id: string;
  label: string;

  /**
   * ID of the parent node that this summary belongs to
   */
  parent: string;

  /**
   * Start index (in the parent's children array) of the summarized nodes
   */
  start: number;

  /**
   * End index (in the parent's children array) of the summarized nodes
   */
  end: number;
}

export interface Arrow {
  id: string;

  /**
   * Label for the arrow (optional descriptive text)
   */
  label: string;

  /**
   * ID of the source node
   */
  from: Uid;

  /**
   * ID of the target node
   */
  to: Uid;

  /**
   * Offset of the Bezier control point from the source node
   */
  delta1: {
    x: number;
    y: number;
  };

  /**
   * Offset of the Bezier control point from the target node
   */
  delta2: {
    x: number;
    y: number;
  };

  /**
   * Indicates if the arrow is bidirectional
   */
  bidirectional?: boolean;
}
```

---

Use a **JSON** object in the format:

```ts
{
  nodeData: NodeObj;
  arrows?: Arrow[];
  summaries?: Summary[];
}
```

to reply to the user. This structure represents a **mind map**, built recursively.

---

**Important Notes!!**
`nodeData`, `arrows`, and `summaries` must be on the **same hierarchical level**.

### Guidelines:

* Use incrementing numbers for node IDs (e.g., 1, 1-1, 2…).
* Avoid creating only sibling relationships — introduce **parent-child hierarchies** appropriately.
* `Summary` is used to summarize **multiple sibling nodes** under the same parent. It is rendered with a curly-brace bracket alongside the grouped nodes.
* `Arrow` is used to connect **any two nodes**, regardless of hierarchy.
* Add `Summary` and `Arrow` entries where suitable to enrich the structure.

")]
        mindmap_data: String,
    ) -> String {
        let _ = self.app.emit(
            "operation",
            MindmapEvent::GenerateMindmap {
                mindmap_data: &mindmap_data,
            },
        );
        let e = once_ready(self.app.clone()).await;
        e.payload().to_string()
    }
    #[tool(description = "Edit Topic")]
    async fn edit_topic(
        &self,
        #[tool(param)]
        #[schemars(description = "Node ID")]
        node_id: String,
        #[tool(param)]
        #[schemars(description = "Topic")]
        topic: String,
    ) -> std::string::String {
        let _ = self.app.emit(
            "operation",
            MindmapEvent::EditTopic {
                node_id: &node_id,
                topic: &topic,
            },
        );
        let e = once_ready(self.app.clone()).await;
        e.payload().to_string()
    }
    #[tool(description = "Add Child Node")]
    async fn add_child(
        &self,
        #[tool(param)]
        #[schemars(description = "Parent ID")]
        parent_id: String,
        #[tool(param)]
        #[schemars(description = "Topic")]
        topic: String,
    ) -> std::string::String {
        let _ = self.app.emit(
            "operation",
            MindmapEvent::AddChild {
                parent_id: &parent_id,
                topic: &topic,
            },
        );
        let e = once_ready(self.app.clone()).await;
        e.payload().to_string()
    }
    #[tool(description = "Add Node Summary")]
    async fn add_node_summary(
        &self,
        #[tool(param)]
        #[schemars(
            description = "Summary text should be a concise and informative summary of the child nodes."
        )]
        text: String,
        #[tool(param)]
        #[schemars(description = "Parent node ID")]
        parent: String,
        #[tool(param)]
        #[schemars(description = "Start index of child nodes to summarize (0-based)")]
        start: i8,
        #[tool(param)]
        #[schemars(description = "End index of child nodes to summarize (0-based)")]
        end: i8,
    ) -> std::string::String {
        let _ = self.app.emit(
            "operation",
            MindmapEvent::CreateSummary {
                text: &text,
                parent: &parent,
                start,
                end,
            },
        );
        let e = once_ready(self.app.clone()).await;
        e.payload().to_string()
    }
    #[tool(description = "Add Arrow Between Nodes")]
    async fn add_arrow(
        &self,
        #[tool(param)]
        #[schemars(
            description = "A descriptive string label for the arrow, used for display purposes"
        )]
        label: String,
        #[tool(param)]
        #[schemars(description = "The unique identifier (Uid) of the starting node")]
        from: String,
        #[tool(param)]
        #[schemars(description = "The unique identifier (Uid) of the ending node")]
        to: String,
        #[tool(param)]
        #[schemars(description = "Whether the arrow represents a bidirectional connection")]
        bidirectional: bool,
    ) -> std::string::String {
        let _ = self.app.emit(
            "operation",
            MindmapEvent::CreateArrow {
                label: &label,
                from: &from,
                to: &to,
                bidirectional,
            },
        );
        let e = once_ready(self.app.clone()).await;
        e.payload().to_string()
    }
}

#[tool(tool_box)]
impl ServerHandler for Mindmap {
    fn get_info(&self) -> ServerInfo {
        ServerInfo {
            instructions: Some(
                "This is a mindmap app controller. You can use it to manipulate mindmap".into(),
            ),
            capabilities: ServerCapabilities::builder().enable_tools().build(),
            ..Default::default()
        }
    }
}
