import { MindElixirData, NodeObj } from 'mind-elixir'
import { writeTextFile, BaseDirectory, readTextFile, readDir, copyFile, remove, mkdir, exists, stat } from '@tauri-apps/plugin-fs'
import { templates } from '@/templates'
import { useSearchMapStore } from '@/store'
import { searchTag, searchTopic } from '@/utils/mind-elixir'

export const dirInit = async () => {
  // debugger
  const baseExists = await exists('', {
    baseDir: BaseDirectory.AppData,
  })
  if (!baseExists) {
    await mkdir('', {
      baseDir: BaseDirectory.AppData,
    })
  }
  const mapExists = await exists('maps', {
    baseDir: BaseDirectory.AppData,
  })
  if (!mapExists) {
    await mkdir('maps', {
      baseDir: BaseDirectory.AppData,
    })
  }
  const trashExists = await exists('trash', {
    baseDir: BaseDirectory.AppData,
  })
  if (!trashExists) {
    await mkdir('trash', {
      baseDir: BaseDirectory.AppData,
    })
  }
}
export type MindElixirLocal = {
  id: string
  content: MindElixirData
  createdAt: number
  updatedAt: number
  source?: string // 数据来源，可以是一个视频地址
  cloudId?: string
}
export type MindElixirLoadError = {
  id: string
  error: string
}

export type MindElixirCloud = {
  __v: number
  _id: string
  author: string
  content: unknown
  date: string
  name: string
  origin: string
  public: boolean
  updatedAt: string
}

export const saveMap = async (id: string, data: MindElixirLocal) => {
  const contents = JSON.stringify(data)
  await writeTextFile(`maps/${id}`, contents, {
    baseDir: BaseDirectory.AppData,
  }).catch(e => {
    console.log('saveMap error', e)
  })
}

export const loadMap = async (dir: string, id: string): Promise<MindElixirLocal | MindElixirLoadError | null> => {
  const str = await readTextFile(`${dir}/${id}`, {
    baseDir: BaseDirectory.AppData,
  }).catch(() => {
    return null
  })

  if (!str) return null

  try {
    return JSON.parse(str) as MindElixirLocal
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error: unknown) {
    return {
      id,
      error: 'parse error',
    } as MindElixirLoadError
  }
}

const getFileList = async (dir: string) => {
  const all = await readDir(dir, { baseDir: BaseDirectory.AppData })
  const statList = all.map(async file => {
    const info = await stat(`${dir}/${file.name}`, {
      baseDir: BaseDirectory.AppData,
    })
    return {
      ...file,
      ...info,
    }
  })
  const list = await Promise.all(statList)
  return list.sort((a, b) => {
    return +b.mtime! - +a.mtime!
  })
}

export const listMaps = async (page: number = 1, size: number = 10): Promise<{ total: number; list: (MindElixirLocal | MindElixirLoadError)[] }> => {
  const all = await getFileList('maps')
  const total = all.length
  const data = all.slice((page - 1) * size, page * size)
  const promises = data.map(file => {
    return readTextFile(`maps/${file.name}`, { baseDir: BaseDirectory.AppData })
  })
  const list = (await Promise.all(promises)).map((str, index) => {
    try {
      return JSON.parse(str) as MindElixirLocal
    } catch {
      return {
        id: data[index].name,
        error: 'parse error',
      } as MindElixirLoadError
    }
  })
  return { total, list }
}

let controller: AbortController | null = null

export const searchMaps = async (dir: string, type: 'topic' | 'tag', keyword: string) => {
  controller?.abort()
  controller = new AbortController()
  const state = useSearchMapStore.getState()
  state.setSearchMap([])
  if (keyword === '') return
  const all = await readDir(`${dir}`, { baseDir: BaseDirectory.AppData })
  for (const file of all) {
    if (controller?.signal.aborted) {
      break
    }
    const str = await readTextFile(`${dir}/${file.name}`, { baseDir: BaseDirectory.AppData })
    let data: MindElixirLocal
    try {
      data = JSON.parse(str) as MindElixirLocal
    } catch {
      // Skip files that can't be parsed
      continue
    }

    const { content } = data
    if (!content) {
      // Skip files without content
      continue
    }

    let matched: string | undefined = undefined
    if (type === 'topic') {
      matched = searchTopic(content.nodeData, keyword)
    } else {
      matched = searchTag(content.nodeData, keyword)
    }
    console.log(matched)
    if (matched) {
      state.appendSearchMap(data)
    }
  }
}

export const deleteMap = async (id: string) => {
  await copyFile(`maps/${id}`, `trash/${id}`, {
    fromPathBaseDir: BaseDirectory.AppData,
    toPathBaseDir: BaseDirectory.AppData,
  })
  await remove(`maps/${id}`, {
    baseDir: BaseDirectory.AppData,
  })
}

export const listTrash = async (page: number = 1, size: number = 10): Promise<{ total: number; list: (MindElixirLocal | MindElixirLoadError)[] }> => {
  const all = await getFileList('trash')
  const total = all.length
  const data = all.slice((page - 1) * size, page * size)
  const promises = data.map(file => {
    return readTextFile(`trash/${file.name}`, { baseDir: BaseDirectory.AppData })
  })
  const list = (await Promise.all(promises)).map((str, index) => {
    try {
      return JSON.parse(str) as MindElixirLocal
    } catch {
      return {
        id: data[index].name,
        error: 'parse error',
      } as MindElixirLoadError
    }
  })
  return { total, list }
}

export const restoreMap = async (id: string) => {
  await copyFile(`trash/${id}`, `maps/${id}`, {
    fromPathBaseDir: BaseDirectory.AppData,
    toPathBaseDir: BaseDirectory.AppData,
  })
  await remove(`trash/${id}`, {
    baseDir: BaseDirectory.AppData,
  })
}

export const deleteTrash = async (id: string) => {
  await remove(`trash/${id}`, {
    baseDir: BaseDirectory.AppData,
  })
}

export const clearTrash = async () => {
  await remove('trash', {
    baseDir: BaseDirectory.AppData,
    recursive: true,
  })
  await mkdir('trash', {
    baseDir: BaseDirectory.AppData,
  })
}

export const listTemplates = async () => {
  return {
    list: templates,
    total: templates.length,
  }
}

const resetId = (data: NodeObj) => {
  data.id = crypto.randomUUID()
  if (data.children) {
    data.children.forEach(resetId)
  }
}

export const loadTemplate = async (fileId: string, templateId: string) => {
  const template = templates.find(t => t.id === templateId)
  if (!template) return null

  const copy: MindElixirLocal = JSON.parse(JSON.stringify(template))
  if (copy.content) {
    resetId(copy.content.nodeData)
  }
  copy.id = fileId
  copy.createdAt = +Date.now()
  copy.updatedAt = +Date.now()

  return copy
}
