export const on = function <K extends keyof GlobalEventHandlersEventMap>(
  list: { dom: EventTarget; evt: K; func: (this: EventTarget, ev: GlobalEventHandlersEventMap[K]) => void }[]
) {
  for (let i = 0; i < list.length; i++) {
    const { dom, evt, func } = list[i]
    dom.addEventListener(evt, func as EventListener)
  }
  return function off() {
    for (let i = 0; i < list.length; i++) {
      const { dom, evt, func } = list[i]
      dom.removeEventListener(evt, func as EventListener)
    }
  }
}
