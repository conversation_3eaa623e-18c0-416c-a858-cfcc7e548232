export interface AiData {
  candidates: Candidate[];
  usageMetadata: UsageMetadata;
  modelVersion: string;
}
interface UsageMetadata {
  promptTokenCount: number;
  totalTokenCount: number;
  promptTokensDetails: PromptTokensDetail[];
}
interface PromptTokensDetail {
  modality: string;
  tokenCount: number;
}
interface Candidate {
  content: Content;
}
interface Content {
  parts: Part[];
  role: string;
}
interface Part {
  text: string;
}