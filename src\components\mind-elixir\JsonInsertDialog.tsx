import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '../ui/dialog'
import { Button } from '../ui/button'
import { Textarea } from '../ui/textarea'
import { Label } from '../ui/label'
import { toast } from 'sonner'
import { Loader2 } from 'lucide-react'
import type { NodeObj } from 'mind-elixir'
import { generateUUID } from '@/utils/common'

interface JsonInsertDialogProps {
  open: boolean
  setOpen: (open: boolean) => void
  onInsert: (data: NodeObj | NodeObj[], isNodeData: boolean) => void
}

export const JsonInsertDialog = ({ open, setOpen, onInsert }: JsonInsertDialogProps) => {
  const { t } = useTranslation()
  const [jsonText, setJsonText] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const handleInsert = async () => {
    if (!jsonText.trim()) {
      toast.error(t('error.emptyJson'))
      return
    }

    setIsLoading(true)
    try {
      const data = JSON.parse(jsonText)
      if (data.nodeData) {
        onInsert(data.nodeData as NodeObj, true)

      }
      // 检查是否是 nodeData 格式（包含 id 和 topic 属性）
      else if (data.id && data.topic) {
        // 这是一个 nodeData，直接替换当前节点
        onInsert(data as NodeObj, true)
      } else if (Array.isArray(data)) {
        // 这是一个 children 数组，添加为子节点
        // 确保每个子节点都有 id
        const processedData = data.map(child => ({
          ...child,
          id: child.id || generateUUID()
        }))
        onInsert(processedData as NodeObj[], false)
      } else {
        toast.error(t('error.invalidJsonFormat'))
        setIsLoading(false)
        return
      }

      toast.success(t('success.jsonInserted'))
      setOpen(false)
      setJsonText('')
    } catch (error) {
      console.error('JSON parse error:', error)
      toast.error(t('error.invalidJson'))
    }
    setIsLoading(false)
  }

  const handleClose = () => {
    setOpen(false)
    setJsonText('')
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{t('mindmap.insertJson')}</DialogTitle>
          <DialogDescription>
            {t('mindmap.insertJsonDescription')}
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="json">{t('common.pasteJson')}</Label>
            <Textarea
              id="json"
              placeholder={`{\n  "id": "node1",\n  "topic": "New Node",\n  "children": [...]\n}\n\n或\n\n[\n  {"id": "child1", "topic": "Child 1"},\n  {"id": "child2", "topic": "Child 2"}\n]`}
              value={jsonText}
              onChange={e => setJsonText(e.target.value)}
              className="min-h-[200px] font-mono text-sm"
            />
            <div className="text-sm text-muted-foreground">
              {t('mindmap.jsonInsertHint')}
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            {t('common.cancel')}
          </Button>
          <Button onClick={handleInsert} disabled={isLoading}>
            {isLoading && <Loader2 className="animate-spin mr-2" size={16} />}
            {t('common.insert')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}