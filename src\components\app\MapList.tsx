import { Import, Plus } from 'lucide-react'
import { Button } from '../ui/button'
import { useLocation } from 'wouter'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { listMaps } from '@/api/map'
import { PayDialog } from './PayDialog'
import { ImportDialog } from './ImportDialog'
import { generateFileId } from '@/utils/common'
import { invalidateTagCache } from './TagList'
import { check } from '@/utils/check'
import BaseListView, { BaseListViewConfig } from './BaseListView'

const MapList = () => {
  const { t } = useTranslation()
  const [, navigate] = useLocation()
  const [dialogOpen, setDialogOpen] = useState(false)
  const [importDialogOpen, setImportDialogOpen] = useState(false)
  const [total, setTotal] = useState(0)

  const createMap = async () => {
    const isPass = await check()
    // 给足数量给机会用户留存，让用户有沉没成本
    if (!isPass && total > 24) {
      setDialogOpen(true)
    } else {
      const id = generateFileId()
      navigate('/edit?id=' + id)
    }
  }

  const importMap = () => {
    setImportDialogOpen(true)
  }

  const config: BaseListViewConfig = {
    listApi: listMaps,
    searchDir: 'maps',
    cardType: 'map',
    onListChange: (newTotal) => {
      setTotal(newTotal)
      invalidateTagCache()
    },
    mobileHeightOffset: 180,
    desktopHeightOffset: 56,
    mobileMinHeightOffset: 280,
    desktopMinHeightOffset: 140,
    actionButtons: (
      <div className="flex gap-2">
        <Button size="sm" className="h-8 flex-1 sm:flex-none" onClick={createMap}>
          <Plus className="h-4 w-4 sm:mr-1" />
          <span className="hidden sm:inline">{t('mindmap.newMap')}</span>
        </Button>
        <Button size="sm" className="h-8 flex-1 sm:flex-none" onClick={importMap}>
          <Import className="h-4 w-4 sm:mr-1" />
          <span className="hidden sm:inline">{t('mindmap.importMap')}</span>
        </Button>
      </div>
    ),
    dialogs: (
      <>
        <PayDialog open={dialogOpen} setOpen={setDialogOpen} />
        <ImportDialog open={importDialogOpen} setOpen={setImportDialogOpen} />
      </>
    ),
    emptyStateContent: (
      <div
        className="border rounded-md p-3 flex flex-col items-center justify-center aspect-square bg-background hover:bg-accent/50 cursor-pointer"
        onClick={createMap}
      >
        <Plus className="h-6 w-6 mb-1 text-muted-foreground" />
        <span className="text-xs text-muted-foreground">{t('mindmap.newMap')}</span>
      </div>
    )
  }

  return <BaseListView config={config} />
}

export default MapList
