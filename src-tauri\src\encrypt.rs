use aes_gcm::aead::Aead;
use aes_gcm::{Aes256Gcm, Key, KeyInit, Nonce}; // Aes128Gcm, Aes192Gcm 也是可选项，取决于你的密钥长度
use base64::engine::general_purpose;
use base64::Engine;
use serde::Serialize;

#[derive(Serialize)]
pub struct StatusResponse {
    pub is_pass: bool,
    pub exp: String,
    pub err: String,
}

#[tauri::command]
pub fn decrypt_data(encrypted_data_base64: String) -> Result<String, String> {
    let encrypted_bytes_with_nonce = general_purpose::STANDARD
        .decode(encrypted_data_base64)
        .map_err(|e| format!("Base64 decode failed: {}", e))?;

    let key_bytes = b"c12a45f78cabddefa143233687a0c1e2";
    let key = Key::<Aes256Gcm>::from_slice(key_bytes);
    let cipher = Aes256Gcm::new(&key);

    // 从 Go 过来的数据是 nonce || ciphertext
    // Nonce 的标准长度是 12 字节
    let nonce_size = 12; // AesGcm 标准 Nonce 长度为 12 字节
    if encrypted_bytes_with_nonce.len() < nonce_size {
        return Err("wrong Nonce".to_string());
    }

    let (nonce_bytes, ciphertext) = encrypted_bytes_with_nonce.split_at(nonce_size);
    let nonce = Nonce::from_slice(nonce_bytes);

    let plaintext = cipher
        .decrypt(nonce, ciphertext)
        .map_err(|e| format!("decrypt: {}", e))?; // 解密失败也意味着认证失败

    let plaintext_str =
        String::from_utf8(plaintext).map_err(|e| format!("UTF-8 decode failed: {}", e))?;
    Ok(plaintext_str)
}

#[tauri::command]
pub async fn get_status(text: String) -> StatusResponse {
    let decrypted = match decrypt_data(text) {
        Ok(data) => data,
        Err(err) => {
            return StatusResponse {
                is_pass: false,
                exp: "".to_string(),
                err: err,
            }
        }
    };

    let json: serde_json::Value = match serde_json::from_str(&decrypted) {
        Ok(json) => json,
        Err(err) => {
            return StatusResponse {
                is_pass: false,
                exp: "".to_string(),
                err: format!("JSON parsing error: {}", err),
            }
        }
    };

    let exp = match json["exp"].as_i64() {
        Some(exp) => exp.to_string(),
        None => {
            return StatusResponse {
                is_pass: false,
                exp: "".to_string(),
                err: "Missing 'exp' field in JSON".to_string(),
            }
        }
    };

    let type_field = match json["type"].as_str() {
        Some(field) => field,
        None => {
            return StatusResponse {
                is_pass: false,
                exp: "".to_string(),
                err: "".to_string(),
            }
        }
    };

    if type_field.is_empty() {
        return StatusResponse {
            is_pass: false,
            exp,
            err: "".to_string(),
        };
    }

    StatusResponse {
        is_pass: true,
        exp,
        err: "".to_string(),
    }
}
