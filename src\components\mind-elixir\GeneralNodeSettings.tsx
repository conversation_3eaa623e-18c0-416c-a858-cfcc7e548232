import MindElixir, { <PERSON>de<PERSON>bj } from 'mind-elixir'
import { Input } from '../ui/input'
import { FormLabel, FormDescription, FormControl, FormField, FormItem, FormMessage, Form } from '../ui/form'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { ColorPicker } from '../unofficial/color-picker'
import { Slider } from '../ui/slider'
import { useMemo, useState } from 'react'
import { Button } from '../ui/button'
import { ToggleGroup, ToggleGroupItem } from '../ui/toggle-group'
import { ScrollArea } from '../ui/scroll-area'
import { useMindElixirStore } from '@/store'
import debounce from '@/utils/debounce'
import { useTranslation } from 'react-i18next'

const taskProgressEmojis = [
  '⭐',
  '✅',
  '❌',
  '⚠️',
  '📝',
  '💡',
  '📌',
  '🔍',
  '🔥',
  '📅',
  '⏳',
  '🔒',
  '🚀',
  '🌟',
  '🛠️',
  '📚',
  '🎯',
  '🖍️',
  '💬',
  '🌍',
  '🎨',
  '❤️',
  '🔄',
  '🎉',
]

const formSchema = z.object({
  color: z.string(),
  background: z.string(),
  url: z.string(),
  tags: z.array(z.string()),
  icons: z.array(z.string()),
})

const GeneralNodeSettings = () => {
  const { t } = useTranslation()
  const node = useMindElixirStore(state => state.currentNode)
  const mei = useMindElixirStore(state => state.mei)
  const [, forceUpdate] = useState(0)
  const nodeUpdate = <K extends keyof NodeObj>(key: K, value: NodeObj[K]) => {
    if (!node || !mei) return
    node[key] = value
    mei.reshapeNode(MindElixir.E(node.id), {
      [key]: value,
    })
    forceUpdate(prev => prev + 1)
  }

  const onNodeUpdate = debounce(nodeUpdate, 200)

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {},
  })

  const tpcStyle = useMemo(() => {
    if (!node) return null
    const tpc = MindElixir.E(node.id)
    return getComputedStyle(tpc)
  }, [node])

  const fontSize = node?.style?.fontSize || tpcStyle?.fontSize // 带 px

  return (
    <ScrollArea className="h-full overflow-auto">
      <div className="p-3 pt-1">
        <Form {...form}>
          <form className="space-y-5">
            <FormField
              control={form.control}
              name="color"
              render={() => (
                <FormItem>
                  <FormLabel>{t('generalNodeSettings.color')}</FormLabel>
                  <FormControl>
                    <ColorPicker value={node?.style?.color || tpcStyle?.color} onChange={color => onNodeUpdate('style', { ...node?.style, color })} />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="background"
              render={() => (
                <FormItem>
                  <FormLabel>{t('generalNodeSettings.backgroundColor')}</FormLabel>
                  <div className="flex gap-3">
                    <FormControl>
                      <ColorPicker
                        value={node?.style?.background || tpcStyle?.backgroundColor}
                        onChange={background => onNodeUpdate('style', { ...node?.style, background })}
                      />
                    </FormControl>
                    <Button variant="ghost" type="button" onClick={() => onNodeUpdate('style', { ...node?.style, background: '' })}>
                      {t('generalNodeSettings.clear')}
                    </Button>
                  </div>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="background"
              render={() => (
                <FormItem>
                  <FormLabel>{t('generalNodeSettings.fontSize')}</FormLabel>
                  <div className="flex gap-3">
                    <span>{fontSize}</span>
                    <FormControl>
                      <Slider
                        defaultValue={[parseInt(fontSize || '16px')]}
                        min={10}
                        max={30}
                        step={1}
                        onValueChange={value => {
                          onNodeUpdate('style', {
                            ...node?.style,
                            fontSize: value[0] + 'px',
                          })
                        }}
                      />
                    </FormControl>
                  </div>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="tags"
              render={() => (
                <FormItem>
                  <FormLabel>{t('generalNodeSettings.tags')}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t('generalNodeSettings.tags')}
                      value={node?.tags?.join(',') || ''}
                      onChange={e => nodeUpdate('tags', e.target.value ? e.target.value.split(',') : undefined)}
                    />
                  </FormControl>
                  <FormDescription>{t('generalNodeSettings.tagsDescription')}</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="icons"
              render={() => (
                <FormItem>
                  <FormLabel>{t('generalNodeSettings.icons')}</FormLabel>
                  <FormControl>
                    <ToggleGroup
                      className="flex-wrap"
                      type="multiple"
                      value={node?.icons}
                      onValueChange={value => {
                        onNodeUpdate('icons', value)
                      }}
                    >
                      {taskProgressEmojis.map(emj => {
                        return (
                          <ToggleGroupItem key={emj} value={emj} aria-label="emj">
                            {emj}
                          </ToggleGroupItem>
                        )
                      })}
                    </ToggleGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="url"
              render={() => (
                <FormItem>
                  <FormLabel>{t('generalNodeSettings.url')}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t('generalNodeSettings.url')}
                      value={node?.hyperLink || ''}
                      onChange={e => nodeUpdate('hyperLink', e.target.value)}
                    />
                  </FormControl>
                  <FormDescription>{t('generalNodeSettings.urlDescription')}</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>
      </div>
    </ScrollArea>
  )
}

export default GeneralNodeSettings
