const AddChildIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_4_8)">
      <path
        d="M21 12.0182L17.0001 11.9818M21 12.0182L17.0001 11.9818"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path d="M19.0001 10V14M19.0001 10V14" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path
        d="M12.3333 9H5.66667C4.74619 9 4 9.89543 4 11V13C4 14.1046 4.74619 15 5.66667 15H12.3333C13.2538 15 14 14.1046 14 13V11C14 9.89543 13.2538 9 12.3333 9Z"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path d="M4 12H2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </g>
    <defs>
      <clipPath id="clip0_4_8">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
)

export default AddChildIcon
