import { req } from './req'
import { MindElixirCloud, MindElixirLocal } from './map'
import { UserInfo } from '@/store'

export const fetchSharedList = async () => {
  // const res = await fetch('https://mind-elixir-backend.fly.dev/api/public?pageSize=10&page=1&name=')
  const res = await req('/api/public?pageSize=10&page=1&name=')
  const { data } = await res.json()
  return (data as MindElixirCloud[]).map(item => {
    return {
      ...item,
      id: item._id,
      createdAt: +new Date(item.date),
      updatedAt: +new Date(item.updatedAt),
    }
  })
}

export const fetchUserInfo = async () => {
  const res = await req(`/api/user`)
  const json = await res.json()
  return json.data as UserInfo
}

export const fetchDesktop = async () => {
  const res = await req(`/api/desktop-user`)
  const json = await res.json()
  return json.data as string
}

export const fetchShared = async (id: string) => {
  const res = await req(`/api/public/${id}`)
  const json = await res.json()
  return json.data as MindElixirLocal
}

export const uploadMindMap = async (meData: MindElixirLocal) => {
  const content = meData.content
  const data = {
    name: content.nodeData.topic,
    content: content,
    source: meData.source,
  }
  const res = await req(`/api/map`, {
    method: 'POST',
    body: JSON.stringify(data),
  })

  const json = await res.json()
  return json.data as {
    _id: string
  }
}

export const updateMindMap = async (id: string, meData: MindElixirLocal) => {
  const content = meData.content
  const data = {
    name: content.nodeData.topic,
    content: content,
    source: meData.source,
  }
  const res = await req(`/api/map/${id}`, {
    method: 'PATCH',
    body: JSON.stringify(data),
  })

  const json = await res.json()
  return json.data as {
    _id: string
  }
}
