export interface OpenAIResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Choice[];
  usage?: Usage;
}

interface Choice {
  index: number;
  delta: Delta;
  finish_reason: string | null;
}

interface Delta {
  content?: string;
  role?: string;
}

interface Usage {
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
}

// For SSE streaming responses
export interface OpenAIStreamingResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: StreamingChoice[];
}

interface StreamingChoice {
  index: number;
  delta: StreamingDelta;
  finish_reason: string | null;
}

interface StreamingDelta {
  content?: string;
  reasoning_content?: string;
  role?: string;
}