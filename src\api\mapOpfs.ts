import { MindElixirData, NodeObj } from 'mind-elixir'
import { OTFile } from 'node_modules/opfs-tools/dist/file'
import { file, dir, write } from 'opfs-tools'
import { templates } from '@/templates'
import { useSearchMapStore } from '@/store'
import { searchTag, searchTopic } from '@/utils/mind-elixir'

export const dirInit = async () => {
  await dir('maps')
  await dir('trash')
}

export type MindElixirLocal = {
  id: string
  content: MindElixirData
  createdAt: number
  updatedAt: number
  cloudId?: string
}

export type MindElixirCloud = {
  __v: number
  _id: string
  author: string
  content: unknown
  date: string
  name: string
  origin: string
  public: boolean
  updatedAt: string
}

export const saveMap = async (id: string, data: MindElixirLocal) => {
  await dir('maps')
  await write(`maps/${id}`, JSON.stringify(data))
}

export const loadMap = async (dirPath: string, id: string) => {
  await dir(dirPath)
  const str = await file(`${dirPath}/${id}`).text().catch(() => {
    return null
  })
  return str ? (JSON.parse(str) as MindElixirLocal) : null
}

export const listMaps = async (page: number = 1, size: number = 10) => {
  await dir('maps')
  const all = await dir('maps').children()
  const total = all.length
  const data = all.slice((page - 1) * size, page * size)
  const promises = data.map(file => {
    return (file as OTFile).text()
  })
  const list = (await Promise.all(promises)).map(str => {
    return JSON.parse(str) as MindElixirLocal
  })
  return { total, list }
}

let controller: AbortController | null = null

export const searchMaps = async (dirPath: string, type: 'topic' | 'tag', keyword: string) => {
  controller?.abort()
  controller = new AbortController()
  const state = useSearchMapStore.getState()
  state.setSearchMap([])
  if (keyword === '') return

  await dir(dirPath)
  const all = await dir(dirPath).children()

  for (const fileItem of all) {
    if (controller?.signal.aborted) {
      break
    }
    const str = await (fileItem as OTFile).text()
    const data = JSON.parse(str) as MindElixirLocal
    const { content } = data
    let matched: string | undefined = undefined
    if (type === 'topic') {
      matched = searchTopic(content.nodeData, keyword)
    } else {
      matched = searchTag(content.nodeData, keyword)
    }

    if (matched) {
      state.appendSearchMap(data)
    }
  }
}

export const deleteMap = async (id: string) => {
  await dir('trash')
  await file(`maps/${id}`).moveTo(dir('trash'))
}

export const listTrash = async (page: number = 1, size: number = 10) => {
  await dir('trash')
  const all = await dir('trash').children()
  const total = all.length
  const data = all.slice((page - 1) * size, page * size)
  const promises = data.map(file => {
    return (file as OTFile).text()
  })
  const list = (await Promise.all(promises)).map(str => {
    return JSON.parse(str) as MindElixirLocal
  })
  return { total, list }
}

export const restoreMap = async (id: string) => {
  await file(`trash/${id}`).moveTo(dir('maps'))
}

export const deleteTrash = async (id: string) => {
  await file(`trash/${id}`).remove()
}

export const clearTrash = async () => {
  await dir('trash').remove()
  await dir('trash')
}

export const listTemplates = async () => {
  return {
    list: templates,
    total: templates.length,
  }
}

const resetId = (data: NodeObj) => {
  data.id = crypto.randomUUID()
  if (data.children) {
    data.children.forEach(resetId)
  }
}

export const loadTemplate = async (fileId: string, templateId: string) => {
  const template = templates.find(t => t.id === templateId)
  const copy: MindElixirLocal = JSON.parse(JSON.stringify(template))
  resetId(copy.content.nodeData)
  copy.id = fileId
  copy.createdAt = +Date.now()
  copy.updatedAt = +Date.now()

  return copy || null
}
