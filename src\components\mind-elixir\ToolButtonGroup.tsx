import { MindElixirInstance } from 'mind-elixir'
import Center from '@/svg/Center'
import FullScreen from '@/svg/FullScreen'
import LeftIcon from '@/svg/LeftIcon'
import RightIcon from '@/svg/RightIcon'
import SideIcon from '@/svg/SideIcon'
import ZoomOut from '@/svg/ZoomOut'
import ZoomIn from '@/svg/ZoomIn'
import { Button } from '../ui/button'
import { useMindElixirStore } from '@/store'
import { Map, Search } from 'lucide-react'
import { getCurrentWindow } from '@tauri-apps/api/window'
import { useTranslation } from 'react-i18next'

// Create a store action for controlling search dialog
const toggleSearch = () => {
  const store = useMindElixirStore.getState()
  store.toggleSearchOpen()
}

const tools = [
  {
    label: 'fullScreen',
    async onClick() {
      await getCurrentWindow().setFullscreen(true)
    },
    icon: FullScreen,
  },
  {
    label: 'center',
    onClick(mei: MindElixirInstance) {
      mei.toCenter()
    },
    icon: Center,
  },
  {
    label: 'zoomOut',
    onClick(mei: MindElixirInstance) {
      if (mei.scaleVal < 0.6) return
      mei.scale((mei.scaleVal -= 0.2))
    },
    icon: ZoomOut,
  },
  {
    label: 'zoomIn',
    onClick(mei: MindElixirInstance) {
      if (mei.scaleVal > 1.6) return
      mei.scale((mei.scaleVal += 0.2))
    },
    icon: ZoomIn,
  },
  {
    label: 'left',
    onClick(mei: MindElixirInstance) {
      mei.initLeft()
    },
    icon: LeftIcon,
  },
  {
    label: 'right',
    onClick(mei: MindElixirInstance) {
      mei.initRight()
    },
    icon: RightIcon,
  },
  {
    label: 'side',
    onClick(mei: MindElixirInstance) {
      mei.initSide()
    },
    icon: SideIcon,
  },
  {
    label: 'search',
    onClick() {
      toggleSearch()
    },
    icon: Search,
  },
]

const ToolButtonGroup = ({ mei, className }: { mei: MindElixirInstance; className?: string }) => {
  console.log('ToolButtonGroup render')
  const { t } = useTranslation()
  return (
    <div className={className + ' border border-input'}>
      {tools.map((tool, index) => (
        <Button title={t(`tools.${tool.label.toLowerCase()}`)} className="p-1" variant="ghost" key={index} onClick={() => tool.onClick(mei)}>
          <tool.icon />
        </Button>
      ))}
      <Button className="p-1" variant="ghost" onClick={() => useMindElixirStore.getState().toggleUseMiniMap()} title={t('tools.minimap')}>
        <Map className="h-6 w-6" />
      </Button>
    </div>
  )
}

export default () => {
  const mei = useMindElixirStore(state => state.mei)
  return mei ? (
    <div className="absolute left-4 bottom-4 flex flex-col">
      <ToolButtonGroup className="flex flex-col bg-slate-50 dark:bg-slate-950 p-1 rounded-md" mei={mei} />
    </div>
  ) : null
}
