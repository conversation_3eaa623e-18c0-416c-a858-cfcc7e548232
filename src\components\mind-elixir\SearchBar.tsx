import { useState, useEffect, useRef } from 'react'
import { Input } from '../ui/input'
import { Button } from '../ui/button'
import { ChevronDown, ChevronUp, Search, X } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import { NodeObj, MindElixirInstance } from 'mind-elixir'
import MindElixir from 'mind-elixir'
import { searchId } from '@/utils/mind-elixir'

type SearchResult = {
  id: string
  topic: string
  path: string[]
}

interface SearchBarProps {
  open: boolean
  setOpen: (open: boolean) => void
  mei: MindElixirInstance | undefined
}

const SearchBar = ({ open, setOpen, mei }: SearchBarProps) => {
  const { t } = useTranslation()
  const [searchTerm, setSearchTerm] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [selectedIndex, setSelectedIndex] = useState(-1)
  const inputRef = useRef<HTMLInputElement>(null)

  // Focus input when search bar opens
  useEffect(() => {
    if (open && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus()
      }, 100)
    }
  }, [open])

  // Reset when closed
  useEffect(() => {
    if (!open) {
      setSearchTerm('')
      setResults([])
      setSelectedIndex(-1)
    }
  }, [open])

  // Search through the mind map nodes
  const searchNodes = (term: string) => {
    if (!mei || !term.trim()) {
      setResults([])
      return
    }

    const data = mei.getData()
    if (!data.nodeData) return

    const searchResults: SearchResult[] = []

    // Recursive function to search through nodes
    const traverse = (node: NodeObj, path: string[] = []) => {
      // Check if node topic includes search term
      if (node.topic && node.topic.toLowerCase().includes(term.toLowerCase())) {
        searchResults.push({
          id: node.id,
          topic: node.topic,
          path: [...path],
        })
      }

      // Traverse children
      if (node.children) {
        for (const child of node.children) {
          traverse(child, [...path, node.topic || ''])
        }
      }
    }

    traverse(data.nodeData)
    setResults(searchResults)
    setSelectedIndex(searchResults.length > 0 ? 0 : -1)

    // Navigate to first result automatically
    if (searchResults.length > 0) {
      navigateToNode(searchResults[0].id)
    }
  }

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const term = e.target.value
    setSearchTerm(term)
    searchNodes(term)
  }

  // Navigate to the selected node
  const navigateToNode = (nodeId: string) => {
    if (!mei) return

    let nodeObj = searchId(mei.nodeData, nodeId)
    if (!nodeObj) return
    let isDirty = false
    while (nodeObj.parent) {
      if (nodeObj.parent.expanded !== true) {
        nodeObj.parent.expanded = true
        isDirty = true
      }
      nodeObj = nodeObj.parent
    }
    if (isDirty) {
      mei.refresh()
    }
    const el = MindElixir.E(nodeId)
    mei.selectNode(el)
  }

  // Handle navigation with keyboard
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setOpen(false)
    } else if (e.key === 'Enter' && results.length > 0) {
      e.preventDefault()
      if (e.shiftKey) {
        handlePrevious()
      } else {
        handleNext()
      }
    }
  }

  // Handle next/previous button clicks
  const handleNext = () => {
    if (results.length > 0) {
      const newIndex = (selectedIndex + 1) % results.length
      setSelectedIndex(newIndex)
      navigateToNode(results[newIndex].id)
    }
  }

  const handlePrevious = () => {
    if (results.length > 0) {
      const newIndex = (selectedIndex - 1 + results.length) % results.length
      setSelectedIndex(newIndex)
      navigateToNode(results[newIndex].id)
    }
  }

  if (!open) return null

  return (
    <div className="fixed top-16 left-3 z-50 flex items-center bg-background border rounded-md shadow-md" onKeyDown={handleKeyDown}>
      <div className="flex items-center p-2 gap-1 w-72">
        <Search className="h-4 w-4 shrink-0 text-muted-foreground" />
        <Input
          ref={inputRef}
          value={searchTerm}
          onChange={handleSearchChange}
          placeholder={t('search.placeholder')}
          className="h-8 flex-1 border-none focus-visible:ring-0 focus-visible:ring-offset-0"
        />

        {results.length > 0 && (
          <span className="text-xs text-muted-foreground whitespace-nowrap">
            {t('search.results', { current: selectedIndex + 1, total: results.length })}
          </span>
        )}

        <div className="flex items-center gap-1">
          <Button variant="ghost" size="icon" className="h-7 w-7" onClick={handlePrevious} disabled={results.length === 0}>
            <ChevronUp className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" className="h-7 w-7" onClick={handleNext} disabled={results.length === 0}>
            <ChevronDown className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" className="h-7 w-7" onClick={() => setOpen(false)}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}

export default SearchBar
