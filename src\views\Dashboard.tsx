import { Home, Settings, ShoppingBag, Tag, Trash, Users, Menu, X } from 'lucide-react'
import { Link, Route, useRoute } from 'wouter'
import MapList from '@/components/app/MapList'
import TrashList from '@/components/app/TrashList'
import UserSettings from '@/components/app/UserSettings'
import { useTranslation } from 'react-i18next'
import TemplateList from '@/components/app/TemplateList'
import HubList from '@/components/app/HubList'
import UserInfo from '@/components/app/UserInfo'
import { isDev } from '@/utils/common'
import TagList from '@/components/app/TagList'
import icon from '../assets/icon.png'
import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'

type NavItem = {
  href: string
  icon: React.ReactNode
  label: string
  beta?: boolean
}

export default function Dashboard() {
  // 会员模式：无会员最高创建 10 个思维导图
  // todo: 定期 backup
  const { t } = useTranslation()
  const [, params] = useRoute('/:path')
  const path = params?.path || 'maps' // 设置默认值为 maps

  // 移动端侧边栏状态
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  // 检测屏幕尺寸
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768) // md断点
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)

    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // 移动端点击导航项时关闭菜单
  const handleNavClick = () => {
    if (isMobile) {
      setIsMobileMenuOpen(false)
    }
  }

  const navItems: NavItem[] = [
    { href: '/maps', icon: <Home className="h-4 w-4" />, label: t('common.dashboard') },
    { href: '/tags', icon: <Tag className="h-4 w-4" />, label: t('common.tags') },
    { href: '/recycle-bin', icon: <Trash className="h-4 w-4" />, label: t('common.recycleBin') },
    { href: '/templates', icon: <ShoppingBag className="h-4 w-4" />, label: 'Templates', beta: true },
    { href: '/hub', icon: <Users className="h-4 w-4" />, label: 'Hub', beta: true },
    { href: '/settings', icon: <Settings className="h-4 w-4" />, label: t('settings.title') },
  ]

  return (
    <div className="flex h-screen bg-background text-sm">
      {/* Mobile Header */}
      {isMobile && (
        <div className="fixed top-0 left-0 right-0 z-50 bg-background border-b p-3 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <img className="h-6" src={icon} />
            <div className="text-lg font-medium">Mind Elixir</div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="h-8 w-8"
          >
            {isMobileMenuOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
          </Button>
        </div>
      )}

      {/* Mobile Overlay */}
      {isMobile && isMobileMenuOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

      {/* Left Sidebar */}
      <div className={`
        ${isMobile
          ? `fixed top-0 left-0 z-50 h-full w-64 transform transition-transform duration-300 ease-in-out ${
              isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'
            }`
          : 'w-52'
        }
        border-r bg-background p-3 flex flex-col
      `}>
        {/* Desktop Header */}
        {!isMobile && (
          <div className="flex items-center gap-2 mb-4 h-10">
            <img className="h-6" src={icon} />
            <div className="text-xl truncate">Mind Elixir</div>
          </div>
        )}

        {/* Mobile Header in Sidebar */}
        {isMobile && (
          <div className="flex items-center justify-between mb-4 h-10">
            <div className="flex items-center gap-2">
              <img className="h-6" src={icon} />
              <div className="text-lg font-medium">Mind Elixir</div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsMobileMenuOpen(false)}
              className="h-8 w-8"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        )}

        <nav className="space-y-1">
          {navItems
            .filter(item => {
              if (isDev) return true
              else return !item.beta
            })
            .map(item => (
              <Link
                key={item.href}
                href={item.href}
                onClick={handleNavClick}
                className={`flex items-center gap-2 rounded-md px-2 py-1 text-foreground hover:bg-accent hover:text-accent-foreground ${
                  '/' + path === item.href ? 'bg-accent text-accent-foreground' : ''
                }`}
              >
                {item.icon}
                {item.label}
              </Link>
            ))}
        </nav>

        <UserInfo />
      </div>

      {/* Main Content */}
      <div className={`flex-1 flex flex-col overflow-hidden ${isMobile ? 'pt-16' : ''}`}>
        <Route path="/maps">
          <MapList />
        </Route>
        <Route path="/tags">
          <TagList />
        </Route>
        <Route path="/recycle-bin">
          <TrashList />
        </Route>
        <Route path="/templates">
          <TemplateList />
        </Route>
        <Route path="/hub">
          <HubList />
        </Route>
        <Route path="/settings">
          <UserSettings />
        </Route>
      </div>
    </div>
  )
}
