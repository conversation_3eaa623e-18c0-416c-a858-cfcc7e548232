import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '../ui/dialog'
import ShortcutTable from './ShortcutTable'
import { useTranslation } from 'react-i18next'
import { ScrollArea } from '../ui/scroll-area'
import { useEffect, useState } from 'react'

export const ShortcutsDialog = ({ open, setOpen }: { open: boolean; setOpen: (open: boolean) => void; showHint?: boolean }) => {
  const { t } = useTranslation()
  const [maxHeight, setMaxHeight] = useState('80vh')

  // Calculate max height based on window size
  useEffect(() => {
    const updateMaxHeight = () => {
      // Set max height to 80% of viewport height
      setMaxHeight(`${window.innerHeight * 0.8}px`)
    }

    updateMaxHeight()
    window.addEventListener('resize', updateMaxHeight)
    return () => window.removeEventListener('resize', updateMaxHeight)
  }, [])

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent onEscapeKeyDown={() => setOpen(false)} className="max-h-[80vh] flex flex-col" style={{ height: maxHeight }}>
        <DialogHeader>
          <DialogTitle>{t('menu.shortcuts')}</DialogTitle>
          <DialogDescription className="text-center font-medium text-amber-500">{t('shortcuts.hint', { key: 'shift + /' })}</DialogDescription>
        </DialogHeader>
        <ScrollArea className="flex-1 overflow-auto pr-4">
          <ShortcutTable />
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}

export default ShortcutsDialog
