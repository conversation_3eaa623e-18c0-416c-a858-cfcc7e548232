{"common": {"save": "Speichern", "cancel": "Abbrechen", "delete": "Löschen", "edit": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON>", "searchMindMap": "Mind Map durchsuchen...", "searchTags": "Tags durchsuchen...", "tags": "Tags", "allTags": "Alle Tags", "noTagsFound": "Keine Tags gefunden.", "noMapsWithTag": "<PERSON>ine Mind Maps mit diesem Tag.", "clearFilter": "<PERSON><PERSON>", "back": "Zurück", "system": "System", "email": "E-Mail", "username": "<PERSON><PERSON><PERSON><PERSON>", "avatar": "Avatar", "password": "Passwort", "logout": "Abmelden", "dashboard": "Dashboard", "recycleBin": "Papierkorb", "noData": "<PERSON><PERSON>", "recycleBinEmpty": "Der Papierkorb ist leer.", "templatesEmpty": "<PERSON><PERSON>lagen verfügbar.", "emptyRecycleBin": "<PERSON><PERSON><PERSON><PERSON><PERSON> leeren", "preview": "Vorschau", "restore": "Wiederherstellen", "deletePermanently": "Dauerhaft löschen", "lastEdited": "Zuletzt bearbeitet", "upload": "Hochladen", "file": "<PERSON><PERSON>", "link": "Link", "selectFile": "<PERSON>i ausw<PERSON>hlen", "enterLink": "<PERSON>", "import": "Importieren", "download": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clipboard": "Zwischenablage", "pasteJson": "JSON einfügen", "dataCorrupted": "<PERSON>n besch<PERSON>", "cannotPreview": "Vorschau nicht möglich", "error": "<PERSON><PERSON>"}, "mindmap": {"newMap": "<PERSON><PERSON>", "importMap": "Importieren", "importMapDescription": "Mind Map aus JSON-Datei oder URL importieren", "openMap": "Mind Map ö<PERSON>nen", "addNode": "Knoten hinzufügen", "deleteNode": "Knoten löschen", "addChild": "Unterknoten hinzufügen", "addSibling": "Geschwisterknoten hinzufügen", "centerMap": "<PERSON><PERSON> zentrieren", "zoomIn": "Vergrößern", "zoomOut": "Verkleinern", "mindMap": "Mind Map", "outline": "Gliederung", "saveAsNewMap": "Als neue Karte s<PERSON>ichern", "mindMapUpdated": "Mind Map aktualisiert", "mindMapUploaded": "Mind Map hochgeladen", "outlineMode": "Gliederungsmodus", "saved": "Gespe<PERSON>rt", "importFailed": "Import fehlgeschlagen", "savedLocally": "Lokal gespeichert", "newMindMap": "Neue Mind Map"}, "nodeMenu": {"general": "Allgemein", "note": "Notiz", "image": "Bild", "selectNode": "Bitte wählen Si<PERSON> einen Knoten aus.", "globalSettings": "Globale Einstellungen", "mindMapSettings": "Mind Map Einstellungen", "theme": "<PERSON>a", "source": "<PERSON><PERSON>", "fileInfo": "Datei-Info", "createdAt": "Erstellt am", "updatedAt": "Aktualisiert am", "enterSource": "<PERSON><PERSON> (z.B. Video-URL)"}, "dialog": {"confirmDeletion": "Löschung bestätigen", "clearRecycleBinConfirmation": "Sind <PERSON> sic<PERSON>, dass Sie den Papierkorb leeren möchten? Diese Aktion kann nicht rückgängig gemacht werden.", "limitReached": "<PERSON>it erreicht", "subscriptionRequired": "Sie haben mehr als 12 Mind Maps erstellt. Bitte abonnieren Sie, um unbegrenzten Zugang freizuschalten und weitere zu erstellen.", "maybeLater": "Vielleicht später", "subscribeNow": "Jetzt abonnieren", "unsavedChangesAlert": "Sie haben ungespeicherte Änderungen. Möchten Sie wirklich beenden?"}, "operation": {"addChild": "Unterknoten hinzufügen", "addParent": "Übergeordneten Knoten hinzufügen", "addSibling": "Geschwisterknoten hinzufügen", "remove": "Entfernen", "focus": "Fokussieren", "unfocus": "<PERSON>okus aufheben", "moveUp": "Nach oben verschieben", "moveDown": "Nach unten verschieben", "link": "Verknüpfen", "linkTips": "Bitte klicken Sie auf den zu verknüpfenden Knoten", "summary": "Zusammenfassung"}, "contextMenu": {"ai": "KI-Erstellung", "aiWithPrompt": "KI-Erstellung mit Prompt", "generate": "Generieren!", "prompt": "Prompt", "addChild": "Unterknoten hinzufügen", "addParent": "Übergeordneten Knoten hinzufügen", "addSibling": "Geschwisterknoten hinzufügen", "remove": "Entfernen", "focus": "Fokussieren", "unfocus": "<PERSON>okus aufheben", "moveUp": "Nach oben verschieben", "moveDown": "Nach unten verschieben", "link": "Verknüpfen", "summary": "Zusammenfassung"}, "menu": {"mindElixir": "MindElixir", "open": "<PERSON><PERSON><PERSON>", "export": "Exportieren", "theme": "Design", "appearance": "Erscheinungsbild", "system": "System", "light": "Hell", "dark": "<PERSON><PERSON><PERSON>", "shortcuts": "Tastenkürzel", "exit": "<PERSON>den"}, "shortcuts": {"action": "Aktion", "shortcut": "Tastenkürzel", "displaySide": "Seitenleiste anzeigen", "displayLeft": "<PERSON><PERSON> anzeigen", "displayRight": "Rechts anzeigen", "save": "Speichern", "undo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON><PERSON>", "paste": "Einfügen", "cut": "Ausschneiden", "moveToCenter": "<PERSON><PERSON> <PERSON> bewegen", "editTopic": "<PERSON><PERSON> bearbeiten", "zoomIn": "Vergrößern", "zoomOut": "Verkleinern", "resetZoom": "Zoom zurücksetzen", "toggleNodeMenu": "Knotenmenü umschalten", "showShortcutsDialog": "Tastenkürzel-Dialog anzeigen", "toggleOutlineMode": "Gliederungsmodus umschalten", "hint": "<PERSON><PERSON><PERSON> {{key}}, um diesen Dialog erneut anzuzeigen"}, "settings": {"title": "Einstellungen", "theme": "Design", "light": "Hell", "dark": "<PERSON><PERSON><PERSON>", "account": {"title": "Ko<PERSON>", "changeEmail": "E-Mail ändern", "changePassword": "Passwort ändern", "pleaseLogin": "Anmelden", "logout": "Abmelden", "userProfile": "Benutzerprofil", "upgradeToPro": "Auf Pro upgraden", "checkLicense": "Lizenz prüfen", "githubLogin": "Github-<PERSON><PERSON><PERSON><PERSON>", "googleLogin": "Google-Anmeldung"}, "appearance": {"title": "Erscheinungsbild", "darkMode": "Dunkler Modus", "darkModeDescription": "Zwischen hellem und dunklem Modus wechseln", "theme": "Design", "selectTheme": "Design auswählen", "light": "Hell", "dark": "<PERSON><PERSON><PERSON>", "system": "System"}, "proxy": {"title": "Proxy", "enable": "Proxy aktivieren", "url": "Proxy-URL"}, "ai": {"title": "KI-Einstellungen", "mode": "KI-Modus", "geminiKey": "Gemini API-Schlüssel", "openaiKey": "OpenAI API-Schlüssel", "openaiModel": "OpenAI-Modell", "baseUrl": "Basis-URL", "aiProvider": "KI-Anbieter", "normal": "Normal", "contextAware": "Kontextbewusste Erweiterung", "openaiCompatible": "OpenAI-kompatibel", "selectModel": "<PERSON><PERSON> auswählen", "loading": "Modelle werden geladen...", "errorFetchingModels": "Fehler beim Abrufen der Modelle", "searchModel": "Modell suchen...", "noModelFound": "<PERSON><PERSON> gefunden.", "geminiModel": "Gemini-Modell", "checkSettings": "Bitte überprüfen Sie die KI-Einstellungen", "gemini": "Gemini", "openai": "OpenAI", "manualInputRequired": "<PERSON><PERSON><PERSON> kann nicht abgerufen werden, bitte manuell eingeben"}, "notifications": {"title": "Benachrichtigungen", "emailNotifications": "E-Mail-Benachrichtigungen", "emailDescription": "E-Mails über Ihre Aktivitäten erhalten", "pushNotifications": "Push-Benachrichtigungen", "pushDescription": "Benachrichtigungen über neue Funktionen erhalten"}, "language": {"title": "<PERSON><PERSON><PERSON>", "preferred": "Bevorzugte Sprache", "selectLanguage": "Sprache auswählen", "select": "Sprache auswählen"}, "privacy": {"title": "Datenschutz", "publicProfile": "Öffentliches Profil", "publicProfileDescription": "<PERSON><PERSON><PERSON> ermöglichen, <PERSON>hre Mind Maps zu sehen", "shareAnalytics": "Analysedaten teilen", "shareAnalyticsDescription": "<PERSON><PERSON><PERSON> un<PERSON>, unser Produkt zu verbessern"}, "storage": {"title": "Datenspeicher", "openDataFolder": "Datenordner öffnen", "openDataFolderDescription": "Den Ordner öffnen, in dem Ihre Mind Maps gespeichert sind"}, "about": {"title": "<PERSON><PERSON>", "version": "Version"}}, "auth": {"loginSuccess": "Anmeldung erfolgreich", "logoutSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON> erfo<PERSON>g<PERSON>ich"}, "error": {"general": "Ein Fehler ist aufgetreten", "importFailed": "Import fehlgeschlagen, möglicherweise aufgrund eines Formatfehlers", "unauthorized": "<PERSON><PERSON><PERSON><PERSON> ab<PERSON><PERSON><PERSON>, bitte melden Sie sich erneut an"}, "import": {"jsonFileDescription": "JSON-Datei aus Mind Elixir importieren", "fileDescriptionBatch": "Unterstützt Batch-I<PERSON><PERSON> von JSON- und XMind-Dateien", "linkDescription": "Aus Mind Elixir Cloud importieren", "clipboardDescription": "JSON-Inhalt einfügen", "batchTags": "Batch-Tags", "batchTagsPlaceholder": "<PERSON><PERSON> e<PERSON>, durch <PERSON><PERSON><PERSON> getrennt", "batchTagsDescription": "Gleiche Tags zu allen importierten Dateien hinzufügen für einfachere Kategorisierung in der Tag-Ansicht", "batchImportSuccess": "{{count}} <PERSON><PERSON> er<PERSON>g<PERSON> importiert", "batchImportPartialSuccess": "{{success}} <PERSON><PERSON> erfolgreich importiert, {{fail}} <PERSON><PERSON> konnten nicht importiert werden", "batchImportFailed": "Batch-I<PERSON>rt fehlgeschlagen, alle Dateien konnten nicht importiert werden"}, "search": {"placeholder": "In Mind Map suchen...", "noResults": "<PERSON><PERSON> gefunden", "results": "{{current}}/{{total}} Ergebnisse"}, "tips": {"pressF1ToCenter": "Drücken Sie F1, um zur Mind Map-Mitte zurückzukehren"}}