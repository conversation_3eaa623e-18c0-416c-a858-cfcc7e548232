import { deleteMap, deleteTrash, MindElixirLocal, restoreMap, saveMap } from '@/api/map'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu'
import { Button } from '../ui/button'
import { MoreHorizontal } from 'lucide-react'
import { useEffect, useRef } from 'react'
import MindElixir, { MindElixirInstance } from 'mind-elixir'
import { formatTime, isDev } from '@/utils/common'
import { updateMindMap, uploadMindMap } from '@/api/cloud'
import { toast } from 'sonner'
import { useTranslation } from 'react-i18next'
import { downloadMethodList } from '@/utils/download'

const PreviewCard = ({ map, onDone, type }: { map: MindElixirLocal; onDone: () => void; type: 'map' | 'trash' }) => {
  const { t } = useTranslation()
  const divRef = useRef<HTMLDivElement>(null)
  const meiRef = useRef<MindElixirInstance | null>(null)
  useEffect(() => {
    if (!divRef.current) return
    const mei = new MindElixir({
      el: divRef.current,
      editable: false,
      draggable: false,
      contextMenu: false,
      toolBar: false,
    })
    // todo: should not make data dirty when init
    mei.init(map.content)
    mei.scale(0.2)
    meiRef.current = mei
    const handleResize = () => {
      mei.toCenter()
    }
    window.addEventListener('resize', handleResize)
    return () => {
      mei.destroy()
      meiRef.current = null
      window.removeEventListener('resize', handleResize)
    }
  }, [map])
  return (
    <div className="relative border rounded-md overflow-hidden bg-background group">
      <div className="absolute top-1 right-1 z-10">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="h-6 w-6 bg-blue-50/40 dark:bg-blue-950/40" onClick={e => e.stopPropagation()}>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuSub>
              <DropdownMenuSubTrigger>{t('common.download')}</DropdownMenuSubTrigger>
              <DropdownMenuPortal>
                <DropdownMenuSubContent>
                  {downloadMethodList.map(item => {
                    return (
                      <DropdownMenuItem key={item.type} onClick={() => item.download(meiRef.current!)}>
                        {item.type}
                      </DropdownMenuItem>
                    )
                  })}
                </DropdownMenuSubContent>
              </DropdownMenuPortal>
            </DropdownMenuSub>
            {type === 'trash' ? (
              <>
                <DropdownMenuItem
                  onClick={async e => {
                    e.stopPropagation()
                    await restoreMap(map.id)
                    onDone()
                  }}
                >
                  {t('common.restore')}
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={async e => {
                    e.stopPropagation()
                    await deleteTrash(map.id)
                    onDone()
                  }}
                >
                  {t('common.deletePermanently')}
                </DropdownMenuItem>
              </>
            ) : (
              <>
                <DropdownMenuItem
                  onClick={async e => {
                    e.stopPropagation()
                    await deleteMap(map.id)
                    onDone()
                  }}
                >
                  {t('common.delete')}
                </DropdownMenuItem>
                {isDev && (
                  <DropdownMenuItem
                    onClick={async e => {
                      e.stopPropagation()
                      if (!meiRef.current) return
                      const data = map.content
                      if (map.cloudId) {
                        await updateMindMap(map.cloudId, map)
                        toast.success(t('mindmap.mindMapUpdated'))
                      } else {
                        const res = await uploadMindMap(map)
                        map.cloudId = res._id
                        map.content = data
                        saveMap(map.id, map)
                        toast.success(t('mindmap.mindMapUploaded'))
                        onDone()
                      }
                    }}
                  >
                    {t('common.upload')}
                  </DropdownMenuItem>
                )}
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      <div
        className="aspect-square bg-muted"
        onClick={() => {
          window.history.pushState(null, '', `/${type === 'map' ? 'edit' : 'trash'}?id=${map.id}`)
        }}
      >
        <div className="aspect-square pointer-events-none" ref={divRef}>
          {t('common.preview')}
        </div>
      </div>
      <div className="p-2">
        <h3 className="font-medium truncate">{map.content.nodeData.topic}</h3>
        <p className="text-xs text-muted-foreground">
          {t('common.lastEdited')}: {formatTime(map.updatedAt)}
        </p>
      </div>
    </div>
  )
}

export default PreviewCard
