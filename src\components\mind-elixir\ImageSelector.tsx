import { useEffect, useState } from 'react'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { ReloadIcon } from '@radix-ui/react-icons'
import { useMindElixirStore } from '@/store'
import MindElixir, { NodeObj } from 'mind-elixir'
import { Link2, Link2Off } from 'lucide-react'
import { useTranslation } from 'react-i18next'

const maxImageWidth = 200
const fitOptions = ['fill', 'contain', 'cover'] as const
const ImageSelector = () => {
  const { t } = useTranslation()
  const currentNode = useMindElixirStore(state => state.currentNode)
  const mei = useMindElixirStore(state => state.mei) 
  const [image, setImage] = useState('')
  const [width, setWidth] = useState(0)
  const [height, setHeight] = useState(0)
  const [fit, setFit] = useState('')
  const [isLinking, setIsLinking] = useState(true)
  const [isLoading, setIsLoading] = useState(false)
  useEffect(() => {
    if (currentNode?.image) {
      setImage(currentNode.image.url || '')
      setWidth(currentNode.image.width || 0)
      setHeight(currentNode.image.height || 0)
      setFit(currentNode.image.fit || '')
    }
  }, [currentNode])
  const reshape = (image: NodeObj['image']) => {
    if (mei && currentNode)
      mei.reshapeNode(MindElixir.E(currentNode.id), {
        image,
      })
  }
  return (
    <div className="flex flex-col gap-3">
      <label className="text-sm font-medium leading-none">{t('imageSelector.imageUrl')}</label>
      <Input placeholder={t('imageSelector.imageUrl')} value={image} onChange={e => setImage(e.target.value)} />
      <Button
        disabled={isLoading}
        onClick={() => {
          console.log('Selected image:', image)
          const img = new Image()
          img.src = image
          setIsLoading(true)
          img.onload = () => {
            setIsLoading(false)
            const width = img.width > maxImageWidth ? maxImageWidth : img.width
            const height = (img.height * width) / img.width
            setWidth(width)
            setHeight(height)
            reshape({
              url: image,
              width,
              height,
            })
          }
          img.onerror = () => {
            setIsLoading(false)
            console.error('Failed to load image:', image)
          }
        }}
      >
        {isLoading && <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />}
        {t('imageSelector.fetchImage')}
      </Button>
      <label className="text-sm font-medium leading-none">{t('imageSelector.widthHeight')}</label>
      <div className="flex">
        <Input
          disabled={!width}
          type="number"
          placeholder={t('imageSelector.width')}
          value={width}
          onChange={e => {
            const w = Number(e.target.value)
            setWidth(w)
            if (isLinking) {
              const h = (height * w) / width
              setHeight(h)
            }
          }}
          onBlur={() =>
            reshape({
              url: image,
              width,
              height,
            })
          }
        />
        <Button variant="ghost" onClick={() => setIsLinking(!isLinking)}>
          {isLinking ? <Link2 /> : <Link2Off />}
        </Button>
        <Input
          disabled={!height}
          type="number"
          placeholder={t('imageSelector.height')}
          value={height}
          onChange={e => {
            const h = Number(e.target.value)
            setHeight(h)
            if (isLinking) {
              const w = (width * h) / height
              setWidth(w)
            }
          }}
          onBlur={() =>
            reshape({
              url: image,
              width,
              height,
            })
          }
        />
      </div>
      <label className="text-sm font-medium leading-none">{t('imageSelector.objectFit')}</label>
      <Select
        value={fit}
        onValueChange={value => {
          setFit(value)
          reshape({
            url: image,
            width,
            height,
            fit: (value as (typeof fitOptions)[number]) || undefined,
          })
        }}
      >
        <SelectTrigger className="w-full">
          <SelectValue placeholder="--" />
        </SelectTrigger>
        <SelectContent>
          {fitOptions.map(item => (
            <SelectItem value={item} key={item}>{item || 'None'}</SelectItem>
          ))}
        </SelectContent>
      </Select>
      <Button
        variant="outline"
        onClick={() => {
          setFit('')
          setWidth(0)
          setHeight(0)
          setImage('')

          reshape(undefined)
        }}
      >
        {t('imageSelector.clearImage')}
      </Button>
    </div>
  )
}

export default ImageSelector
