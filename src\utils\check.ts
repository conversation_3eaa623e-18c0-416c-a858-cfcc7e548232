// pay wall check

import { fetchDesktop } from '@/api/cloud'
import { invoke } from '@tauri-apps/api/core'

interface StatusResponse {
  is_pass: boolean
  exp: string
  err: string
}
let retry = 0
export const check = async (): Promise<boolean> => {
  // debugger
  let text = localStorage.getItem('dd')
  if (!text) {
    text = await fetchDesktop().catch(err => {
      console.error('fetchDesktop error', err)
      return ''
    })
    localStorage.setItem('dd', text)
  }
  const response = await invoke<StatusResponse>('get_status', { text })

  // Log error message if present
  if (response.err) {
    console.error('Status check error:', response.err)
  }

  /**
   * 相同的text一个月后会被判过期，过期后清理
   */
  if (response.is_pass && response.exp) {
    const expTimestamp = parseInt(response.exp)
    const currentTime = Math.floor(Date.now() / 1000) // Current time in seconds
    if (expTimestamp < currentTime) {
      if (retry > 0) return false
      retry++
      return await reCheck()
    }
  }

  return response.is_pass
}

export const reCheck = async () => {
  const text = await fetchDesktop()
  localStorage.setItem('dd', text)
  return check()
}
